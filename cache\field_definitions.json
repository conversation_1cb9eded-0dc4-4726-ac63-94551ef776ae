[{"hóa đơn bán hàng": {"Master": {"Mã loại hóa đơn": {"desc": "Dựa theo khai báo ký hiệu, mẫu hóa đơn trên portal. VD: 01GTKT là Hóa đơn giá trị gia tăng", "print_name": "loai_hd", "json_tag": "InvoiceType", "xml_tag": ""}, "Tên loại hóa đơn": {"desc": "Tên lo<PERSON>i hóa đơn, dự<PERSON> theo khai báo ký hiệu mẫu hóa đơn trên portal. VD: Hóa đơn giá trị gia tăng, <PERSON><PERSON><PERSON> đơn bán hàng ...v.v.", "print_name": "ten_loai_hd", "json_tag": "InvoiceName", "xml_tag": ""}, "Mẫu số hóa đơn": {"desc": "<PERSON><PERSON><PERSON> trị trường \"mã ký hiệu\" củ<PERSON> \"<PERSON>hai báo ký hiệu mẫu số hóa đơn\" trên portal. VD: 01GTKT0/001, 02GTTT0/001…", "print_name": "mau_so", "json_tag": "InvoicePattern", "xml_tag": ""}, "Ký hiệu hóa đơn": {"desc": "<PERSON><PERSON> hiệu: khai báo trong danh mục quyển portal. VD: AA/19E", "print_name": "ky_hieu", "json_tag": "InvoiceSerial", "xml_tag": ""}, "Số hóa đơn": {"desc": "<PERSON><PERSON> hóa đơn điện tử.", "print_name": "so_hd", "json_tag": "InvoiceNumber", "xml_tag": ""}, "Ngày hóa đơn": {"desc": "<PERSON><PERSON><PERSON> trị ngay_ct dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "ngay_hd", "json_tag": "InvoiceDate", "xml_tag": ""}, "Ngày ký hóa đơn": {"desc": "<PERSON><PERSON><PERSON> phát hành và ký số hóa đơn", "print_name": "ngay_ph", "json_tag": "SignedDate", "xml_tag": ""}, "Hình thức thanh toán": {"desc": "Giá trị client t<PERSON><PERSON><PERSON><PERSON> lên, hoặc trường tên thanh toán trong danh mục thanh toán ( nhập liệu trực tiếp portal )", "print_name": "hinh_thuc_tt", "json_tag": "PaymentMethod", "xml_tag": ""}, "Tên Đơn vị bán hàng": {"desc": "<PERSON><PERSON><PERSON> trị trường tên đơn vị : <PERSON><PERSON> thống\\ khai báo thông tin đơn vị , trên portal", "print_name": "ten_cn", "json_tag": "CompanyName", "xml_tag": ""}, "Mã số thuế bên bán": {"desc": "<PERSON><PERSON><PERSON> trị trường mã số thuế : <PERSON><PERSON> thống\\ khai báo thông tin đơn vị , trên portal", "print_name": "mst_cn", "json_tag": "CompanyTaxCode", "xml_tag": "MST"}, "Địa chỉ bên bán": {"desc": "G<PERSON><PERSON> trị trường địa chỉ : <PERSON><PERSON> thống\\ khai báo thông tin đơn vị , trên portal", "print_name": "dia_chi", "json_tag": "CompanyAddress", "xml_tag": ""}, "Điện thoại bên bán": {"desc": "Gi<PERSON> trị trường điện thoại: <PERSON><PERSON> thống\\ khai báo thông tin đơn vị, trên portal.\nTối đa 32 ký tự, nếu KH nhiều số hotline dài quá, thì có thể thiết kế cứng trên mẫu in. Nhưng phải có 1 giá trị nhập liệu portal đúng. Nên dùng biến từ hệ thống + giá trị cứng cần thêm.", "print_name": "dien_thoai", "json_tag": "CompanyPhone", "xml_tag": ""}, "Số fax bên bán": {"desc": "G<PERSON><PERSON> trị trường \"số gửi bản sao (Fax)\": <PERSON><PERSON> thống\\ khai báo thông tin đơn vị, trên portal.\n Tối đa 32 ký tự, nếu KH nhiều số fax dài quá, th<PERSON> có thể thiết kế cứng trên mẫu in. Nhưng phải có 1 giá trị nhập liệu portal đúng. Nên dùng biến từ hệ thống + giá trị cứng cần thêm.", "print_name": "fax", "json_tag": "CompanyFax", "xml_tag": ""}, "Email bên bán": {"desc": "<PERSON><PERSON><PERSON> trị trường \"<PERSON><PERSON><PERSON> (Email)\": <PERSON><PERSON> thống\\ khai báo thông tin đơn vị, trên portal.\nTối đa 128 ký tự. Nếu <PERSON>h nhiều email thì có thể thiết kế cứng trên mẫu in,.Nhưng phải có 1 giá trị nhập liệu portal đúng. Nên dùng biến từ hệ thống + giá trị cứng cần thêm.", "print_name": "email", "json_tag": "CompanyEmail", "xml_tag": ""}, "Số tài khoản ngân hàng bên bán": {"desc": "<PERSON><PERSON><PERSON> trị trường \"Tà<PERSON> khoản ngân hàng\": <PERSON><PERSON> thống\\ khai báo thông tin đơn vị , trên portal.\nTối đa 32 ký tự. KH nhiều tài khoản có thể thiết kế cứng trên mẫu in. nên có 1 số tk đúng nhập liệu trên portal. Nên dùng biến từ hệ thống + giá trị cứng cần thêm.", "print_name": "tk_nh", "json_tag": "CompanyBankAccount", "xml_tag": ""}, "Tên ngân hàng bên bán": {"desc": "<PERSON><PERSON><PERSON> trị trường \"Tên ngân hàng\": <PERSON><PERSON> thống\\ khai báo thông tin đơn vị , trên portal.\nTối đa 128 ký tự. KH nhiều tài khoản ở nghiều ngân hàng có thể thiết kế cứng trên mẫu in. <PERSON>ên dùng biến từ hệ thống + gi<PERSON> trị cứng cần thêm.", "print_name": "ten_nh", "json_tag": "CompanyBankName", "xml_tag": ""}, "Mã khách hàng": {"desc": "Mã khách dưới client t<PERSON><PERSON><PERSON><PERSON> lên. (Nhập liệu trực tiếp portal là mã khách trong danh mục khách hàng)", "print_name": "ma_kh", "json_tag": "CustomerCode", "xml_tag": ""}, "Mã số thuế người mua": {"desc": "Mã số thuế do client t<PERSON><PERSON><PERSON><PERSON> lên ( nhập liệu portal là trường mã số thuế master)", "print_name": "mst_kh", "json_tag": "CustomerTaxCode", "xml_tag": ""}, "Tên người mua hàng": {"desc": "<PERSON>ê<PERSON> ng<PERSON><PERSON>i mua do client t<PERSON><PERSON><PERSON><PERSON> lên ( FBO, FBFF là trường ong_ba master)", "print_name": "nguoi_mua", "json_tag": "Buyer", "xml_tag": ""}, "Tên đơn vị mua hàng": {"desc": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng d<PERSON> client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "ten_kh", "json_tag": "CustomerName", "xml_tag": ""}, "Địa chỉ người mua hàng": {"desc": "<PERSON><PERSON><PERSON> chỉ ngư<PERSON><PERSON> mua, dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "dia_chi_kh", "json_tag": "Customer<PERSON><PERSON><PERSON>", "xml_tag": ""}, "Điện thoại người mua hàng": {"desc": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON><PERSON> ng<PERSON><PERSON> mua, dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "dien_thoai_kh", "json_tag": "CustomerPhone", "xml_tag": ""}, "Số fax người mua hàng": {"desc": "Số fax ng<PERSON><PERSON><PERSON> mua, dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "fax_kh", "json_tag": "CustomerFax", "xml_tag": ""}, "Danh sách email người mua hàng (nhận thông báo phát hành)": {"desc": "<PERSON><PERSON> s<PERSON>ch email ng<PERSON><PERSON><PERSON> mua hàng ( nhận thông báo phát hành) client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "email_kh", "json_tag": "EmailDeliver", "xml_tag": ""}, "Tài khoản ngân hàng người mua": {"desc": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng ng<PERSON><PERSON><PERSON> mua, dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "tk_nh_kh", "json_tag": "CustomerBankAccount", "xml_tag": ""}, "Tên ngân hàng người mua": {"desc": "<PERSON><PERSON><PERSON> ngân hàng ng<PERSON><PERSON><PERSON> mua, dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "ten_nh_kh", "json_tag": "CustomerBankName", "xml_tag": ""}, "Loại khách hàng": {"desc": "<PERSON><PERSON><PERSON>,client t<PERSON><PERSON><PERSON><PERSON> ( 0 / 1 )", "print_name": "loai_kh", "json_tag": "CustomerType", "xml_tag": ""}, "Mã ngoại tệ": {"desc": "<PERSON><PERSON> ng<PERSON><PERSON><PERSON> t<PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "ma_nt", "json_tag": "CurrencyUnit", "xml_tag": ""}, "Tỷ giá": {"desc": "Tỷ g<PERSON><PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. <PERSON><PERSON> thống hỗ trợ numeric (24, 12), ch<PERSON><PERSON><PERSON> THUẾ chỉ cho phép 2 số lẻ (numeric (7,2)).", "print_name": "ty_gia", "json_tag": "ExchangeRate", "xml_tag": ""}, "Tổng tiền hàng": {"desc": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> hà<PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. 6 số lẻ", "print_name": "t_tien", "json_tag": "Amount", "xml_tag": ""}, "Thuế suất": {"desc": "<PERSON><PERSON><PERSON> su<PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. gi<PERSON> trị là dạng số. 0, 5, 8, 10. thue_suat = -1: kh<PERSON><PERSON> chịu thuế, -2: <PERSON><PERSON><PERSON><PERSON> kê khai tính thuế", "print_name": "thue_suat", "json_tag": "TaxRate", "xml_tag": ""}, "Tổng tiền thuế": {"desc": "<PERSON><PERSON><PERSON> tiền thu<PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. 6 số lẻ", "print_name": "t_thue", "json_tag": "TaxAmount", "xml_tag": ""}, "Tổng tiền thanh toán": {"desc": "<PERSON><PERSON><PERSON> tiền <PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. 6 số lẻ", "print_name": "t_thanh_toan", "json_tag": "TotalAmount", "xml_tag": ""}, "Số tiền đọc bằng chữ (Tổng thanh toán)": {"desc": "<PERSON><PERSON> tiền đọc bằng chữ ( tổng <PERSON>h toán), client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "t_tt_in_words", "json_tag": "AmountInWords", "xml_tag": ""}, "Tổng tiền thuế không chịu thuế": {"desc": "<PERSON><PERSON><PERSON> tiền thuế (k<PERSON><PERSON><PERSON> chịu thuế), client truy<PERSON>n lên. 6 số lẻ", "print_name": "t_thue1", "json_tag": "TaxAmountFree", "xml_tag": ""}, "Tổng tiền thuế 0%": {"desc": "Tổng tiền thuế 0%, client t<PERSON><PERSON><PERSON><PERSON> lên. 6 số lẻ", "print_name": "t_thue0", "json_tag": "TaxAmount0", "xml_tag": ""}, "Tổng tiền thuế 5%": {"desc": "T<PERSON>ng tiền thuế 5%, client t<PERSON><PERSON><PERSON><PERSON> lên. 6 số lẻ", "print_name": "t_thue5", "json_tag": "TaxAmount5", "xml_tag": ""}, "Tổng tiền thuế 10%": {"desc": "T<PERSON>ng tiền thuế 10%, client t<PERSON><PERSON><PERSON><PERSON> lên. 6 số lẻ", "print_name": "t_thue10", "json_tag": "TaxAmount10", "xml_tag": ""}, "Tổng tiền chiết khấu": {"desc": "<PERSON><PERSON><PERSON> tiền chi<PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. 6 số lẻ", "print_name": "t_ck", "json_tag": "DiscountAmount", "xml_tag": ""}, "Tổng tiền hàng khuyến mãi": {"desc": "Tổng tiền hàng khu<PERSON><PERSON> mãi, client truy<PERSON>n lên. 6 số lẻ", "print_name": "t_km", "json_tag": "PromotionAmount", "xml_tag": ""}, "Trường chuỗi mở rộng 1": {"desc": "Trường chuỗi mở rộng để dự án customize lưu trữ các thông tin bổ sung", "print_name": "s1", "json_tag": "External1", "xml_tag": ""}, "Trường chuỗi mở rộng 2": {"desc": "Trường chuỗi mở rộng để dự án customize lưu trữ các thông tin bổ sung", "print_name": "s2", "json_tag": "External2", "xml_tag": ""}, "Trường chuỗi mở rộng 3": {"desc": "Trường chuỗi mở rộng để dự án customize lưu trữ các thông tin bổ sung", "print_name": "s3", "json_tag": "External3", "xml_tag": ""}, "Trường số mở rộng 1": {"desc": "Trường số mở rộng để dự án customize lưu trữ các thông tin bổ sung. 6 số lẻ", "print_name": "n1", "json_tag": "NumberExternal1", "xml_tag": ""}, "Trường số mở rộng 2": {"desc": "Trường số mở rộng để dự án customize lưu trữ các thông tin bổ sung. 6 số lẻ", "print_name": "n2", "json_tag": "NumberExternal2", "xml_tag": ""}, "Trường số mở rộng 3": {"desc": "Trường số mở rộng để dự án customize lưu trữ các thông tin bổ sung. 6 số lẻ", "print_name": "n3", "json_tag": "NumberExternal3", "xml_tag": ""}, "Kiểu hóa đơn": {"desc": "<PERSON><PERSON><PERSON> hóa đơn: 1 - <PERSON><PERSON><PERSON> đơn gốc, 2 - <PERSON><PERSON><PERSON> đơn điều chỉnh, 4 - <PERSON><PERSON><PERSON> đơn thay thế", "print_name": "kieu_hd", "json_tag": "KindOfInvoice", "xml_tag": ""}, "Loại điều chỉnh": {"desc": "Loại điều chỉnh: 1 - đ<PERSON><PERSON><PERSON> chỉnh gi<PERSON><PERSON>, 2 - đ<PERSON><PERSON><PERSON> chỉnh tăng, 3 - điều chỉnh thông tin.\nLưu ý: Chỉ với kiểu hóa đơn (kieu_hd) = 2: H<PERSON>a đơn điều chỉnh, thì trường loai_dc này mới có giá trị. Còn với hóa đơn gốc hoặc hóa đơn thay thế trong template xml không xử lý ( giá trị sẽ là 0)", "print_name": "loai_dc", "json_tag": "AdjustmentType", "xml_tag": ""}, "Ngày hóa đơn gốc": {"desc": "<PERSON><PERSON><PERSON> của hóa đơn gốc bị điều chỉnh hoặc bị thay thế, chuỗi định dạng dd/MM/yyyy, VD: 15/06/2020\nTrường này chỉ có trong template xml hóa đơn được phát hành từ ngày 19/11/2019 trở đi, <PERSON><PERSON><PERSON> hóa đơn điều chỉnh/ thay thế được phát hành trước 19/11/2019 sẽ không chứa thẻ dữ liệu này.", "print_name": "ngay_hd_goc", "json_tag": "OrgInvoiceDate", "xml_tag": ""}, "Số hóa đơn gốc": {"desc": "<PERSON>ố của hóa đơn gốc bị điều chỉnh hoặc bị thay thế.\nTrường này chỉ có trong template xml hóa đơn được phát hành từ ngày 19/11/2019 trở đi Các hóa đơn điều chỉnh/ thay thế được phát hành trước 19/11/2019 sẽ không chứa thẻ dữ liệu này.", "print_name": "so_hd_goc", "json_tag": "OrgInvoiceNo", "xml_tag": ""}, "Mẫu số hóa đơn gốc": {"desc": "Mẫu số của hóa đơn gốc bị điều chỉnh hoặc bị thay thế.\nTrường này chỉ có trong template xml hóa đơn được phát hành từ ngày 19/11/2019 trở đi Các hóa đơn điều chỉnh/ thay thế được phát hành trước 19/11/2019 sẽ không chứa thẻ dữ liệu này.", "print_name": "mau_hd_goc", "json_tag": "OrgInvoicePattern", "xml_tag": ""}, "Ký hiệu hóa đơn gốc": {"desc": "<PERSON>ý hiệu của hóa đơn gốc bị điều chỉnh hoặc bị thay thế.\nTrường này chỉ có trong template xml hóa đơn được phát hành từ ngày 19/11/2019 trở đi, <PERSON><PERSON><PERSON> hóa đơn điều chỉnh/ thay thế được phát hành trước 19/11/2019 sẽ không chứa thẻ dữ liệu này.", "print_name": "ky_hieu_hd_goc", "json_tag": "OrgInvoiceSerial", "xml_tag": ""}}, "Detail": {"Số thứ tự dòng chi tiết": {"desc": "<PERSON><PERSON> thứ tự dòng trong chi tiế<PERSON>, đ<PERSON><PERSON><PERSON> portal đ<PERSON>h số tăng tự động từ 1.", "print_name": "line_nbr", "json_tag": "", "xml_tag": ""}, "Số thứ tự (IN)": {"desc": "<PERSON><PERSON> thứ tự dòng trong chi tiế<PERSON> (để IN), stt này client có thể truyền lên, mặc định không truyền portal đ<PERSON>h tự động theo line_nbr. Giá trị là số hoặc rỗng.", "print_name": "stt", "json_tag": "LineNumber", "xml_tag": ""}, "Mã vật tư": {"desc": "<PERSON><PERSON> vật tư, client t<PERSON><PERSON><PERSON><PERSON> lên. <PERSON><PERSON> rộng tối đa 32 ký tự", "print_name": "ma_vt", "json_tag": "ItemCode", "xml_tag": ""}, "Tên vật tư": {"desc": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> t<PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. <PERSON><PERSON> rộng tối đa 500 ký tự", "print_name": "ten_vt", "json_tag": "ItemName", "xml_tag": ""}, "Đơn vị tính": {"desc": "<PERSON><PERSON><PERSON> vị <PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lê<PERSON>. <PERSON><PERSON> rộng tối đa 16 ký tự", "print_name": "dvt", "json_tag": "UnitOfMeasure", "xml_tag": ""}, "Khuyến mãi": {"desc": "<PERSON><PERSON><PERSON> trị để phân biệt là hàng khuyến mãi hay không , client t<PERSON><PERSON><PERSON><PERSON> lên ( char (1))", "print_name": "km_yn", "json_tag": "IsPromotion", "xml_tag": ""}, "Số lượng": {"desc": "<PERSON><PERSON> l<PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. <PERSON><PERSON><PERSON> đa 6 số lẻ", "print_name": "so_luong", "json_tag": "Quantity", "xml_tag": ""}, "Giá": {"desc": "Giá. client t<PERSON><PERSON><PERSON><PERSON>, 6 số lẻ", "print_name": "gia", "json_tag": "Price", "xml_tag": ""}, "Tiền hàng": {"desc": "<PERSON><PERSON><PERSON><PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. tối đa 6 số lẻ", "print_name": "tien", "json_tag": "Amount", "xml_tag": ""}, "Tỷ lệ chiết khấu": {"desc": "Tỷ lệ chiết khấu. client truyền lên. 2 số lẻ", "print_name": "tl_ck", "json_tag": "DiscountRate", "xml_tag": ""}, "Tiền chiết khấu": {"desc": "<PERSON>i<PERSON><PERSON> chiết khấu. client t<PERSON><PERSON><PERSON><PERSON> lên, tối đa 6 số lẻ", "print_name": "ck", "json_tag": "DiscountAmount", "xml_tag": ""}, "Thuế suất": {"desc": "<PERSON><PERSON><PERSON> suất. client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "thue_suat", "json_tag": "TaxRate", "xml_tag": ""}, "Tiền thuế": {"desc": "<PERSON><PERSON><PERSON><PERSON>, client t<PERSON><PERSON><PERSON><PERSON>ê<PERSON>, tối đa 6 số lẻ", "print_name": "thue", "json_tag": "TaxAmount", "xml_tag": ""}, "Ghi chú": {"desc": "<PERSON><PERSON> <PERSON>ú, client truy<PERSON>n lên. dùng cho trường hợp mẫu in khách hàng cần hiển thị 1 cột ghi cú khác dưới chi tiết", "print_name": "ghi_chu", "json_tag": "Note", "xml_tag": ""}, "Trường chuỗi mở rộng 1": {"desc": "Trường chuỗi mở rộng để dự án customize lưu trữ các thông tin bổ sung", "print_name": "s1", "json_tag": "External1", "xml_tag": ""}, "Trường chuỗi mở rộng 2": {"desc": "Trường chuỗi mở rộng để dự án customize lưu trữ các thông tin bổ sung", "print_name": "s2", "json_tag": "External2", "xml_tag": ""}, "Trường chuỗi mở rộng 3": {"desc": "Trường chuỗi mở rộng để dự án customize lưu trữ các thông tin bổ sung", "print_name": "s3", "json_tag": "External3", "xml_tag": ""}, "Trường số mở rộng 1": {"desc": "Trường số mở rộng để dự án customize lưu trữ các thông tin bổ sung. 6 số lẻ", "print_name": "n1", "json_tag": "NumberExternal1", "xml_tag": ""}, "Trường số mở rộng 2": {"desc": "Trường số mở rộng để dự án customize lưu trữ các thông tin bổ sung. 6 số lẻ", "print_name": "n2", "json_tag": "NumberExternal2", "xml_tag": ""}, "Trường số mở rộng 3": {"desc": "Trường số mở rộng để dự án customize lưu trữ các thông tin bổ sung. 6 số lẻ", "print_name": "n3", "json_tag": "NumberExternal3", "xml_tag": ""}}}, "phiếu xuất kho": {"Master": {"Mã loại hóa đơn": {"desc": "Dựa theo khai báo ký hiệu, mẫu hóa đơn trên portal. VD: 03XKNB --> <PERSON><PERSON><PERSON> xuất kho kiêm vận chuyển hàng hóa nội bộ", "print_name": "loai_hd", "json_tag": "InvoiceType", "xml_tag": ""}, "Tên loại hóa đơn": {"desc": "Tên lo<PERSON>i hóa đơn, dựa theo khai báo ký hiệu mẫu hóa đơn trên portal. VD: Phiếu xuất kho kiêm vận chuyển hàng hóa nội bộ", "print_name": "ten_loai_hd", "json_tag": "InvoiceName", "xml_tag": ""}, "Mẫu số hóa đơn": {"desc": "<PERSON><PERSON><PERSON> trị trường \"mã ký hiệu\" củ<PERSON> \"<PERSON>hai báo ký hiệu mẫu số hóa đơn\". VD: 03XKNB0/001, 03XKNB0/002...", "print_name": "mau_so", "json_tag": "InvoicePattern", "xml_tag": ""}, "Ký hiệu hóa đơn": {"desc": "<PERSON><PERSON> hiệu: khai báo trong danh mục quyển portal. VD: AA/19E", "print_name": "ky_hieu", "json_tag": "InvoiceSerial", "xml_tag": ""}, "Số hóa đơn": {"desc": "<PERSON><PERSON> hóa đơn điện tử.", "print_name": "so_hd", "json_tag": "InvoiceNumber", "xml_tag": ""}, "Ngày hóa đơn": {"desc": "<PERSON><PERSON><PERSON> trị ngay_ct dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "ngay_hd", "json_tag": "InvoiceDate", "xml_tag": ""}, "Ngày ký hóa đơn": {"desc": "<PERSON><PERSON><PERSON> phát hành và ký số hóa đơn", "print_name": "ngay_ph", "json_tag": "SignedDate", "xml_tag": ""}, "Tên Đơn vị bán hàng": {"desc": "<PERSON><PERSON><PERSON> trị trường tên đơn vị : <PERSON><PERSON> thống\\ khai báo thông tin đơn vị , trên portal", "print_name": "ten_cn", "json_tag": "CompanyName", "xml_tag": ""}, "Mã số thuế bên bán": {"desc": "<PERSON><PERSON><PERSON> trị trường mã số thuế : <PERSON><PERSON> thống\\ khai báo thông tin đơn vị , trên portal", "print_name": "mst_cn", "json_tag": "CompanyTaxCode", "xml_tag": ""}, "Địa chỉ bên bán": {"desc": "G<PERSON><PERSON> trị trường địa chỉ : <PERSON><PERSON> thống\\ khai báo thông tin đơn vị , trên portal", "print_name": "dia_chi", "json_tag": "CompanyAddress", "xml_tag": ""}, "Điện thoại bên bán": {"desc": "Gi<PERSON> trị trường điện thoại: <PERSON><PERSON> thống\\ khai báo thông tin đơn vị, trên portal.\nTối đa 32 ký tự, nếu KH nhiều số hotline dài quá, thì có thể thiết kế cứng trên mẫu in. Nhưng phải có 1 giá trị nhập liệu portal đúng. Nên dùng biến từ hệ thống + giá trị cứng cần thêm.", "print_name": "dien_thoai", "json_tag": "CompanyPhone", "xml_tag": ""}, "Số fax bên bán": {"desc": "G<PERSON><PERSON> trị trường \"số gửi bản sao (Fax)\": <PERSON><PERSON> thống\\ khai báo thông tin đơn vị, trên portal.\nTối đa 32 ký tự, nếu KH nhiều số fax dài quá, th<PERSON> có thể thiết kế cứng trên mẫu in. Nhưng phải có 1 giá trị nhập liệu portal đúng. Nên dùng biến từ hệ thống + giá trị cứng cần thêm.", "print_name": "fax", "json_tag": "CompanyFax", "xml_tag": ""}, "Email bên bán": {"desc": "<PERSON><PERSON><PERSON> trị trường \"<PERSON><PERSON><PERSON> (Email)\": <PERSON><PERSON> thống\\ khai báo thông tin đơn vị, trên portal.\nTối đa 128 ký tự. Nếu <PERSON>h nhiều email thì có thể thiết kế cứng trên mẫu in,.Nhưng phải có 1 giá trị nhập liệu portal đúng. Nên dùng biến từ hệ thống + giá trị cứng cần thêm.", "print_name": "email", "json_tag": "CompanyEmail", "xml_tag": ""}, "Số tài khoản ngân hàng bên bán": {"desc": "<PERSON><PERSON><PERSON> trị trường \"Tà<PERSON> khoản ngân hàng\": <PERSON><PERSON> thống\\ khai báo thông tin đơn vị , trên portal.\nTối đa 32 ký tự. KH nhiều tài khoản có thể thiết kế cứng trên mẫu in. nên có 1 số tk đúng nhập liệu trên portal. Nên dùng biến từ hệ thống + giá trị cứng cần thêm.", "print_name": "tk_nh", "json_tag": "CompanyBankAccount", "xml_tag": ""}, "Tên ngân hàng bên bán": {"desc": "<PERSON><PERSON><PERSON> trị trường \"Tên ngân hàng\": <PERSON><PERSON> thống\\ khai báo thông tin đơn vị , trên portal.\nTối đa 128 ký tự. KH nhiều tài khoản ở nghiều ngân hàng có thể thiết kế cứng trên mẫu in. <PERSON>ên dùng biến từ hệ thống + gi<PERSON> trị cứng cần thêm.", "print_name": "ten_nh", "json_tag": "CompanyBankName", "xml_tag": ""}, "Mã số thuế người nhận hàng": {"desc": "Mã số thuế do client t<PERSON><PERSON><PERSON><PERSON> lên ( nhập liệu portal là trường mã số thuế master)", "print_name": "ma_so_thue", "json_tag": "TaxCode", "xml_tag": ""}, "Số lệnh/hợp đồng": {"desc": "Số lệnh/hợp đồng dư<PERSON> client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "hd_so", "json_tag": "Order", "xml_tag": ""}, "Ngày (Số lệnh/hợp đồng)": {"desc": "Tr<PERSON><PERSON><PERSON> ngà<PERSON> củ<PERSON> \"<PERSON><PERSON> lệnh/hợ<PERSON> đồng\" dưới client tru<PERSON><PERSON>n lên", "print_name": "hd_ngay", "json_tag": "Date", "xml_tag": ""}, "Của đối tác (Số lệnh/hợp đồng)": {"desc": "Dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "hd_cua", "json_tag": "Partner", "xml_tag": ""}, "Về việc/với": {"desc": "Dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "hd_ve_viec", "json_tag": "Reference", "xml_tag": ""}, "Người vận chuyển": {"desc": "Dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "ten_<PERSON>_chuyen", "json_tag": "Deliverer", "xml_tag": ""}, "Hđ vận chuyển": {"desc": "Dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "hd_<PERSON>_chuyen", "json_tag": "DeliveryContract", "xml_tag": ""}, "Pt vận chuyển": {"desc": "Dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "pt_<PERSON>_chuyen", "json_tag": "Transportation", "xml_tag": ""}, "Xuất tại kho": {"desc": "Dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "xuat_tai_kho", "json_tag": "IssuingSite", "xml_tag": ""}, "Nhập tại kho": {"desc": "Dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "nhap_tai_kho", "json_tag": "ReceivingSite", "xml_tag": ""}, "Người nhận": {"desc": "Dưới client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "nguoi_nhan", "json_tag": "Receiver", "xml_tag": ""}, "Mã ngoại tệ": {"desc": "<PERSON><PERSON> ng<PERSON><PERSON><PERSON> t<PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "ma_nt", "json_tag": "CurrencyUnit", "xml_tag": ""}, "Tỷ giá": {"desc": "Tỷ g<PERSON><PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. <PERSON><PERSON> thống hỗ trợ numeric (24, 12), ch<PERSON><PERSON><PERSON> THUẾ chỉ cho phép 2 số lẻ (numeric (7,2)).", "print_name": "ty_gia", "json_tag": "ExchangeRate", "xml_tag": ""}, "Tổng tiền hàng": {"desc": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> hà<PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. 6 số lẻ", "print_name": "t_tien", "json_tag": "Amount", "xml_tag": ""}, "Số tiền đọc bằng chữ ( tổng thanh toán)": {"desc": "<PERSON><PERSON> tiền đọc bằng chữ ( tổng <PERSON>h toán), client t<PERSON><PERSON><PERSON><PERSON> lên", "print_name": "t_tt_in_words", "json_tag": "AmountInWords", "xml_tag": ""}, "Trường chuỗi mở rộng 1": {"desc": "Trường chuỗi mở rộng để dự án customize lưu trữ các thông tin bổ sung", "print_name": "s1", "json_tag": "External1", "xml_tag": ""}, "Trường chuỗi mở rộng 2": {"desc": "Trường chuỗi mở rộng để dự án customize lưu trữ các thông tin bổ sung", "print_name": "s2", "json_tag": "External2", "xml_tag": ""}, "Trường chuỗi mở rộng 3": {"desc": "Trường chuỗi mở rộng để dự án customize lưu trữ các thông tin bổ sung", "print_name": "s3", "json_tag": "External3", "xml_tag": ""}, "Trường số mở rộng 1": {"desc": "Trường số mở rộng để dự án customize lưu trữ các thông tin bổ sung. 6 số lẻ", "print_name": "n1", "json_tag": "NumberExternal1", "xml_tag": ""}, "Trường số mở rộng 2": {"desc": "Trường số mở rộng để dự án customize lưu trữ các thông tin bổ sung. 6 số lẻ", "print_name": "n2", "json_tag": "NumberExternal2", "xml_tag": ""}, "Trường số mở rộng 3": {"desc": "Trường số mở rộng để dự án customize lưu trữ các thông tin bổ sung. 6 số lẻ", "print_name": "n3", "json_tag": "NumberExternal3", "xml_tag": ""}, "Kiểu hóa đơn": {"desc": "<PERSON><PERSON><PERSON> hóa đơn: 5 - <PERSON><PERSON><PERSON> đơn gốc, 6 - <PERSON><PERSON><PERSON> đơn điều chỉnh, 7 - <PERSON><PERSON><PERSON> đơn thay thế", "print_name": "kieu_hd", "json_tag": "KindOfInvoice", "xml_tag": ""}, "Loại điều chỉnh": {"desc": "Loại điều chỉnh: 1 - đ<PERSON><PERSON><PERSON> chỉnh gi<PERSON><PERSON>, 2 - đ<PERSON><PERSON><PERSON> chỉnh tăng, 3 - điều chỉnh thông tin.\nLưu ý: Chỉ với kiểu hóa đơn (kieu_hd) = 2: H<PERSON>a đơn điều chỉnh, thì trường loai_dc này mới có giá trị. Còn với hóa đơn gốc hoặc hóa đơn thay thế trong template xml không xử lý ( giá trị sẽ là 0)", "print_name": "loai_dc", "json_tag": "AdjustmentType", "xml_tag": ""}, "Ngày hóa đơn gốc": {"desc": "<PERSON><PERSON><PERSON> của hóa đơn gốc bị điều chỉnh hoặc bị thay thế, chuỗi định dạng dd/MM/yyyy, VD: 15/06/2020", "print_name": "ngay_hd_goc", "json_tag": "OrgInvoiceDate", "xml_tag": ""}, "Số hóa đơn gốc": {"desc": "<PERSON><PERSON> của hóa đơn gốc bị điều chỉnh hoặc bị thay thế.", "print_name": "so_hd_goc", "json_tag": "OrgInvoiceNo", "xml_tag": ""}, "Mẫu số hóa đơn gốc": {"desc": "Mẫu số của hóa đơn gốc bị điều chỉnh hoặc bị thay thế.", "print_name": "mau_hd_goc", "json_tag": "OrgInvoicePattern", "xml_tag": ""}, "Ký hiệu hóa đơn gốc": {"desc": "<PERSON><PERSON> hiệu của hóa đơn gốc bị điều chỉnh hoặc bị thay thế.", "print_name": "ky_hieu_hd_goc", "json_tag": "OrgInvoiceSerial", "xml_tag": ""}}, "Detail": {"Số thứ tự dòng chi tiết": {"desc": "<PERSON><PERSON> thứ tự dòng trong chi tiế<PERSON>, đ<PERSON><PERSON><PERSON> portal đ<PERSON>h số tăng tự động từ 1.", "print_name": "line_nbr", "json_tag": "", "xml_tag": ""}, "Số thứ tự (IN)": {"desc": "<PERSON><PERSON> thứ tự dòng trong chi tiế<PERSON> (để IN), stt này client có thể truyền lên, mặc định không truyền portal đ<PERSON>h tự động theo line_nbr. Giá trị là số hoặc rỗng.", "print_name": "stt", "json_tag": "LineNumber", "xml_tag": ""}, "Mã vật tư": {"desc": "<PERSON><PERSON> vật tư, client t<PERSON><PERSON><PERSON><PERSON> lên. <PERSON><PERSON> rộng tối đa 32 ký tự", "print_name": "ma_vt", "json_tag": "ItemCode", "xml_tag": ""}, "Tên vật tư": {"desc": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> t<PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. <PERSON><PERSON> rộng tối đa 500 ký tự", "print_name": "ten_vt", "json_tag": "ItemName", "xml_tag": ""}, "Đơn vị tính": {"desc": "<PERSON><PERSON><PERSON> vị <PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lê<PERSON>. <PERSON><PERSON> rộng tối đa 16 ký tự", "print_name": "dvt", "json_tag": "UnitOfMeasure", "xml_tag": ""}, "Số lượng": {"desc": "<PERSON><PERSON> l<PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. <PERSON><PERSON><PERSON> đa 6 số lẻ", "print_name": "so_luong", "json_tag": "Quantity", "xml_tag": ""}, "Giá": {"desc": "Giá. client t<PERSON><PERSON><PERSON><PERSON>, 6 số lẻ", "print_name": "gia", "json_tag": "Price", "xml_tag": ""}, "Tiền hàng": {"desc": "<PERSON><PERSON><PERSON><PERSON>, client t<PERSON><PERSON><PERSON><PERSON> lên. tối đa 6 số lẻ", "print_name": "tien", "json_tag": "Amount", "xml_tag": ""}, "Ghi chú": {"desc": "<PERSON><PERSON> <PERSON>ú, client truy<PERSON>n lên. dùng cho trường hợp mẫu in khách hàng cần hiển thị 1 cột ghi cú khác dưới chi tiết", "print_name": "ghi_chu", "json_tag": "Note", "xml_tag": ""}, "Trường chuỗi mở rộng 1": {"desc": "Trường chuỗi mở rộng để dự án customize lưu trữ các thông tin bổ sung", "print_name": "s1", "json_tag": "External1", "xml_tag": ""}, "Trường chuỗi mở rộng 2": {"desc": "Trường chuỗi mở rộng để dự án customize lưu trữ các thông tin bổ sung", "print_name": "s2", "json_tag": "External2", "xml_tag": ""}, "Trường chuỗi mở rộng 3": {"desc": "Trường chuỗi mở rộng để dự án customize lưu trữ các thông tin bổ sung", "print_name": "s3", "json_tag": "External3", "xml_tag": ""}, "Trường số mở rộng 1": {"desc": "Trường số mở rộng để dự án customize lưu trữ các thông tin bổ sung. 6 số lẻ", "print_name": "n1", "json_tag": "NumberExternal1", "xml_tag": ""}, "Trường số mở rộng 2": {"desc": "Trường số mở rộng để dự án customize lưu trữ các thông tin bổ sung. 6 số lẻ", "print_name": "n2", "json_tag": "NumberExternal2", "xml_tag": ""}, "Trường số mở rộng 3": {"desc": "Trường số mở rộng để dự án customize lưu trữ các thông tin bổ sung. 6 số lẻ", "print_name": "n3", "json_tag": "NumberExternal3", "xml_tag": ""}}}}, {"InvoiceType": "<PERSON>ã loại hóa đơn", "InvoiceName": "<PERSON><PERSON><PERSON> lo<PERSON>i hóa đơn", "InvoicePattern": "Mẫu số hóa đơn", "InvoiceSerial": "<PERSON><PERSON> <PERSON><PERSON><PERSON> hóa đơn", "InvoiceNumber": "S<PERSON> hóa đơn", "InvoiceDate": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>n", "SignedDate": "<PERSON><PERSON><PERSON> ký hóa đơn", "PaymentMethod": "<PERSON><PERSON><PERSON> thức thanh toán", "CompanyName": "<PERSON><PERSON><PERSON> vị bán hàng", "CompanyTaxCode": "<PERSON><PERSON> số thuế bên bán", "MST": "<PERSON><PERSON> số thuế bên bán", "CompanyAddress": "<PERSON><PERSON><PERSON> chỉ bên bán", "CompanyPhone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i bên bán", "CompanyFax": "Số fax bên bán", "CompanyEmail": "<PERSON><PERSON> bên bán", "CompanyBankAccount": "S<PERSON> tài khoản ngân hàng bên bán", "CompanyBankName": "<PERSON><PERSON><PERSON> ngân hàng bên bán", "CustomerCode": "<PERSON><PERSON> kh<PERSON>ch hàng", "CustomerTaxCode": "<PERSON><PERSON> số thuế người mua", "Buyer": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> mua hàng", "CustomerName": "<PERSON><PERSON><PERSON> đơn vị mua hàng", "CustomerAddress": "Đ<PERSON>a chỉ người mua hàng", "CustomerPhone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i người mua hàng", "CustomerFax": "Số fax người mua hàng", "EmailDeliver": "<PERSON><PERSON> s<PERSON>ch email ng<PERSON><PERSON><PERSON> mua hàng (nhận thông báo phát hành)", "CustomerBankAccount": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng người mua", "CustomerBankName": "<PERSON><PERSON><PERSON> ngân hàng ng<PERSON>ời mua", "CustomerType": "<PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng", "CurrencyUnit": "<PERSON><PERSON> ngo<PERSON>i tệ", "ExchangeRate": "Tỷ giá", "Amount": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "TaxRate": "<PERSON><PERSON><PERSON>", "TaxAmount": "<PERSON><PERSON><PERSON><PERSON> thuế", "TotalAmount": "<PERSON><PERSON><PERSON> tiền thanh toán", "AmountInWords": "<PERSON><PERSON> tiền đọc bằng chữ ( tổng thanh toán)", "TaxAmountFree": "<PERSON><PERSON><PERSON> tiền thuế không chịu thuế", "TaxAmount0": "<PERSON><PERSON>ng tiền thuế 0%", "TaxAmount5": "<PERSON><PERSON>ng tiền thuế 5%", "TaxAmount10": "<PERSON><PERSON><PERSON> tiền thuế 10%", "DiscountAmount": "<PERSON><PERSON><PERSON><PERSON> chi<PERSON>t kh<PERSON>u", "PromotionAmount": "T<PERSON>ng tiền hàng khu<PERSON>ến mãi", "External1": "Trường chuỗi mở rộng 1", "External2": "Trường chuỗi mở rộng 2", "External3": "Trường chuỗi mở rộng 3", "NumberExternal1": "Trường số mở rộng 1", "NumberExternal2": "Trường số mở rộng 2", "NumberExternal3": "Trường số mở rộng 3", "KindOfInvoice": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>n", "AdjustmentType": "<PERSON><PERSON>i điều chỉnh", "OrgInvoiceDate": "<PERSON><PERSON><PERSON> h<PERSON>a đơn gốc", "OrgInvoiceNo": "Số hóa đơn gốc", "OrgInvoicePattern": "Mẫu số hóa đơn gốc", "OrgInvoiceSerial": "<PERSON><PERSON> hi<PERSON><PERSON> hóa đơn gốc", "LineNumber": "<PERSON><PERSON> thứ tự (IN)", "ItemCode": "<PERSON><PERSON> vật tư", "ItemName": "<PERSON><PERSON><PERSON> v<PERSON>t tư", "UnitOfMeasure": "Đơn vị t<PERSON>h", "IsPromotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "Quantity": "Số lượng", "Price": "Giá", "DiscountRate": "Tỷ lệ chiết khấu", "Note": "<PERSON><PERSON><PERSON>", "TaxCode": "<PERSON><PERSON> số thuế người nhận hàng", "Order": "Số lệnh/hợp đồng", "Date": "<PERSON><PERSON><PERSON> (Số lệnh/hợp đồng)", "Partner": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON> (Số lệnh/hợp đồng)", "Reference": "Về việc/với", "Deliverer": "<PERSON><PERSON><PERSON><PERSON> vận chuy<PERSON>n", "DeliveryContract": "<PERSON><PERSON> v<PERSON>n ch<PERSON>n", "Transportation": "<PERSON><PERSON> v<PERSON><PERSON> chuy<PERSON>n", "IssuingSite": "<PERSON><PERSON><PERSON> tại kho", "ReceivingSite": "<PERSON><PERSON><PERSON><PERSON> tạ<PERSON> kho", "Receiver": "<PERSON><PERSON><PERSON><PERSON>n"}, ["<PERSON><PERSON><PERSON> b<PERSON> hàng", "<PERSON><PERSON><PERSON> xu<PERSON>t kho"]]