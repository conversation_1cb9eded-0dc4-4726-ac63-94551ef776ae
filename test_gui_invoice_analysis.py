#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test GUI cho chức năng phân tích hóa đơn
"""

import json
import sys
import os
import tkinter as tk
from tkinter import messagebox
import threading
import time

# Th<PERSON><PERSON> thư mục gốc vào sys.path để import được các module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_invoice_analysis():
    """Test GUI với dữ liệu hóa đơn thực tế"""
    
    # Sample JSON data từ user
    sample_json = {
        "data": {
            "structure": {
                "master": ["Key","InvoiceDate","CustomerCode","Buyer","CustomerName","CustomerTaxCode","CustomerType","Address","PhoneNumber","FaxNumber","EmailDeliver","BankAccount","BankName","PaymentMethod","Currency","ExchangeRate","Amount","TotalAmount","TaxRate","TaxAmount","TaxAmount5","TaxAmount10","AmountInWords","HumanName","DiscountAmount","PromotionAmount","Note","VoucherType","IDCardNo","PassportNo","BuyerUnit"],
                "detail": ["ItemCode","ItemName","UOM","IsPromotion","Quantity","Price","Amount","TaxRate","TaxAmount","DiscountAmount","ProcessType"]
            },
            "invoices": [{
                "master": ["A000005943HDA","07/08/2025","**********","","CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ PHÚ HƯNG","**********",None,"Phòng Số 4, Tầng 21, Tòa Nhà Phú Mỹ Hưng, Số 8 Đường Hoàng Văn Thái, Khu Phố 1, Phường Tân Phú, Quận 7, Thành Phố Hồ Chí Minh, Việt Nam","**********",None,"<EMAIL>","","","TM/CK","VND",1.************,800000.00,864000.00,8.00,64000.00,None,None,"Tám trăm sáu mươi bốn nghìn đồng chẵn","FAST",0.00,0.00,"0ÿ",None,"","",""],
                "detail": [
                    [None,"Carrot Bread","Phần",0,20.000,20000.0000,400000.00,8.00,32000.00,0.00,"1"],
                    [None,"SERVICE FEE MAC LEVEL 1 (661-17548)","Cái",0,20.000,20000.0000,400000.00,8.00,32000.00,0.00,"1"]
                ]
            }]
        },
        "voucherBook": "BMRK25",
        "adjustmentType": 0
    }
    
    print("=== KHỞI ĐỘNG TEST GUI PHÂN TÍCH HÓA ĐƠN ===")
    print("Đang khởi tạo ứng dụng...")
    
    try:
        # Import main application
        from main import App

        # Tạo ứng dụng (App tự tạo root window)
        app = App()
        
        # Hàm để tự động test
        def auto_test():
            try:
                time.sleep(2)  # Đợi GUI load xong
                
                print("✓ GUI đã khởi tạo thành công")
                
                # Tìm tab EInvoice Debugger
                if hasattr(app, 'einvoice_tab'):
                    einvoice_tab = app.einvoice_tab
                    print("✓ Tìm thấy EInvoice Debugger tab")
                else:
                    print("❌ Không tìm thấy EInvoice Debugger tab")
                    app.quit()
                    return
                
                # Chuyển sang tab JSON
                try:
                    einvoice_tab.input_notebook.select(0)  # Chọn tab JSON
                    print("✓ Đã chuyển sang tab JSON")
                except:
                    print("⚠ Không thể chuyển sang tab JSON")
                
                # Điền dữ liệu JSON
                json_text = json.dumps(sample_json, indent=2, ensure_ascii=False)
                einvoice_tab.json_input_text.delete("1.0", tk.END)
                einvoice_tab.json_input_text.insert("1.0", json_text)
                print("✓ Đã điền dữ liệu JSON")
                
                # Chọn loại phiếu
                if einvoice_tab.doc_types:
                    einvoice_tab.doc_type_var.set(einvoice_tab.doc_types[0])
                    print(f"✓ Đã chọn loại phiếu: {einvoice_tab.doc_types[0]}")
                
                # Thực hiện phân tích
                time.sleep(1)
                einvoice_tab.handle_analyze_einvoice()
                print("✓ Đã thực hiện phân tích hóa đơn")
                
                # Kiểm tra kết quả
                time.sleep(1)
                
                # Kiểm tra error log
                error_text = einvoice_tab.error_display.get("1.0", tk.END).strip()
                if error_text:
                    if "Không tìm thấy lỗi nào" in error_text:
                        print("✅ Kết quả: Không có lỗi - ĐÚNG")
                    else:
                        print(f"⚠ Kết quả: Có lỗi - {error_text}")
                else:
                    print("⚠ Không có thông tin lỗi")
                
                # Kiểm tra master grid có dữ liệu
                master_view = einvoice_tab.master_view
                if master_view.get_children():
                    print(f"✓ Master grid có {len(master_view.get_children())} dòng dữ liệu")
                else:
                    print("⚠ Master grid không có dữ liệu")

                # Kiểm tra detail grid có dữ liệu
                detail_view = einvoice_tab.detail_view
                if detail_view.get_children():
                    print(f"✓ Detail grid có {len(detail_view.get_children())} dòng dữ liệu")
                else:
                    print("⚠ Detail grid không có dữ liệu")
                
                print("\n🎉 TEST GUI HOÀN THÀNH THÀNH CÔNG!")
                print("Ứng dụng sẽ tự động đóng sau 5 giây...")
                
                # Đóng ứng dụng sau 5 giây
                app.after(5000, app.quit)

            except Exception as e:
                print(f"❌ Lỗi trong quá trình test: {e}")
                import traceback
                traceback.print_exc()
                app.quit()

        # Chạy auto test trong thread riêng
        test_thread = threading.Thread(target=auto_test, daemon=True)
        test_thread.start()

        # Chạy GUI
        print("✓ Bắt đầu chạy GUI...")
        app.mainloop()
        
        print("✓ GUI đã đóng")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khởi tạo GUI: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_gui():
    """Test GUI thủ công - mở ứng dụng để user test"""
    
    print("=== KHỞI ĐỘNG TEST GUI THỦ CÔNG ===")
    print("Ứng dụng sẽ mở để bạn có thể test thủ công...")
    print("\nHướng dẫn test:")
    print("1. Chuyển sang tab 'EInvoice Debugger'")
    print("2. Chọn tab 'JSON' trong phần input")
    print("3. Dán dữ liệu JSON sau vào:")
    
    sample_json = {
        "data": {
            "structure": {
                "master": ["Key","InvoiceDate","CustomerCode","Buyer","CustomerName","CustomerTaxCode","CustomerType","Address","PhoneNumber","FaxNumber","EmailDeliver","BankAccount","BankName","PaymentMethod","Currency","ExchangeRate","Amount","TotalAmount","TaxRate","TaxAmount","TaxAmount5","TaxAmount10","AmountInWords","HumanName","DiscountAmount","PromotionAmount","Note","VoucherType","IDCardNo","PassportNo","BuyerUnit"],
                "detail": ["ItemCode","ItemName","UOM","IsPromotion","Quantity","Price","Amount","TaxRate","TaxAmount","DiscountAmount","ProcessType"]
            },
            "invoices": [{
                "master": ["A000005943HDA","07/08/2025","**********","","CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ PHÚ HƯNG","**********",None,"Phòng Số 4, Tầng 21, Tòa Nhà Phú Mỹ Hưng, Số 8 Đường Hoàng Văn Thái, Khu Phố 1, Phường Tân Phú, Quận 7, Thành Phố Hồ Chí Minh, Việt Nam","**********",None,"<EMAIL>","","","TM/CK","VND",1.************,800000.00,864000.00,8.00,64000.00,None,None,"Tám trăm sáu mươi bốn nghìn đồng chẵn","FAST",0.00,0.00,"0ÿ",None,"","",""],
                "detail": [
                    [None,"Carrot Bread","Phần",0,20.000,20000.0000,400000.00,8.00,32000.00,0.00,"1"],
                    [None,"SERVICE FEE MAC LEVEL 1 (661-17548)","Cái",0,20.000,20000.0000,400000.00,8.00,32000.00,0.00,"1"]
                ]
            }]
        },
        "voucherBook": "BMRK25",
        "adjustmentType": 0
    }
    
    print(json.dumps(sample_json, indent=2, ensure_ascii=False))
    print("\n4. Chọn loại phiếu 'Hóa đơn bán hàng'")
    print("5. Nhấn nút 'Phân tích'")
    print("6. Kiểm tra kết quả trong các grid và error log")
    
    try:
        from main import App

        app = App()
        app.title("Manual Test GUI Invoice Analysis")
        app.geometry("1400x900")

        print("\n✓ Ứng dụng đã khởi động. Hãy thực hiện test theo hướng dẫn trên.")
        app.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khởi tạo GUI: {e}")
        return False

if __name__ == "__main__":
    print("Chọn loại test:")
    print("1. Auto test (tự động)")
    print("2. Manual test (thủ công)")
    
    choice = input("Nhập lựa chọn (1 hoặc 2): ").strip()
    
    if choice == "1":
        success = test_gui_invoice_analysis()
    elif choice == "2":
        success = test_manual_gui()
    else:
        print("Lựa chọn không hợp lệ. Chạy auto test...")
        success = test_gui_invoice_analysis()
    
    sys.exit(0 if success else 1)
