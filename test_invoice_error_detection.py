#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test khả năng phát hiện lỗi trong chức năng phân tích hóa đơn
"""

import json
import sys
import os

# Thê<PERSON> thư mục gốc vào sys.path để import đượ<PERSON> các module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_error_detection():
    """Test khả năng phát hiện lỗi với dữ liệu có vấn đề"""
    
    print("=== KIỂM TRA PHÁT HIỆN LỖI TRONG HÓA ĐƠN ===\n")
    
    # Test case 1: Tên đơn vị mua hàng trống
    print("1. Test case: Tên đơn vị mua hàng trống...")
    sample_json_empty_customer = {
        "data": {
            "structure": {
                "master": ["Key","InvoiceDate","CustomerCode","Buyer","CustomerName","CustomerTaxCode"],
                "detail": ["ItemCode","ItemName","UOM","IsPromotion","Quantity","Price","Amount","TaxRate","TaxAmount"]
            },
            "invoices": [{
                "master": ["A000005943HDA","07/08/2025","0102594384","","","0102594384"],  # CustomerName trống
                "detail": [
                    [None,"Carrot Bread","Phần",0,20.000,20000.0000,400000.00,8.00,32000.00]
                ]
            }]
        }
    }
    
    test_with_data(sample_json_empty_customer, "Tên đơn vị mua hàng trống")
    
    # Test case 2: Số lượng âm
    print("\n2. Test case: Số lượng âm...")
    sample_json_negative_quantity = {
        "data": {
            "structure": {
                "master": ["Key","InvoiceDate","CustomerCode","Buyer","CustomerName","CustomerTaxCode"],
                "detail": ["ItemCode","ItemName","UOM","IsPromotion","Quantity","Price","Amount","TaxRate","TaxAmount"]
            },
            "invoices": [{
                "master": ["A000005943HDA","07/08/2025","0102594384","","CÔNG TY ABC","0102594384"],
                "detail": [
                    [None,"Carrot Bread","Phần",0,-5.000,20000.0000,400000.00,8.00,32000.00]  # Số lượng âm
                ]
            }]
        }
    }
    
    test_with_data(sample_json_negative_quantity, "Số lượng âm")
    
    # Test case 3: Tổng tiền không khớp
    print("\n3. Test case: Tổng tiền không khớp...")
    sample_json_amount_mismatch = {
        "data": {
            "structure": {
                "master": ["Key","InvoiceDate","CustomerCode","Buyer","CustomerName","Amount","TotalAmount"],
                "detail": ["ItemCode","ItemName","UOM","IsPromotion","Quantity","Price","Amount","TaxRate","TaxAmount"]
            },
            "invoices": [{
                "master": ["A000005943HDA","07/08/2025","0102594384","","CÔNG TY ABC",500000.00,550000.00],  # Amount = 500k
                "detail": [
                    [None,"Carrot Bread","Phần",0,20.000,20000.0000,400000.00,8.00,32000.00],  # Amount = 400k
                    [None,"Service Fee","Cái",0,5.000,10000.0000,50000.00,8.00,4000.00]       # Amount = 50k, tổng = 450k != 500k
                ]
            }]
        }
    }
    
    test_with_data(sample_json_amount_mismatch, "Tổng tiền không khớp")
    
    # Test case 4: Giá âm
    print("\n4. Test case: Giá âm...")
    sample_json_negative_price = {
        "data": {
            "structure": {
                "master": ["Key","InvoiceDate","CustomerCode","Buyer","CustomerName"],
                "detail": ["ItemCode","ItemName","UOM","IsPromotion","Quantity","Price","Amount","TaxRate","TaxAmount"]
            },
            "invoices": [{
                "master": ["A000005943HDA","07/08/2025","0102594384","","CÔNG TY ABC"],
                "detail": [
                    [None,"Carrot Bread","Phần",0,20.000,-20000.0000,400000.00,8.00,32000.00]  # Giá âm
                ]
            }]
        }
    }
    
    test_with_data(sample_json_negative_price, "Giá âm")

def test_with_data(sample_json, test_name):
    """Helper function để test với dữ liệu cụ thể"""
    try:
        from einvoice_debugger_tab import JSON_TO_SQL_MAPPING, DETAIL_SPECIFIC_MAPPING, EInvoiceDebuggerModel
        
        # Tạo parser helper
        class JsonParser:
            def _map_json_to_sql_fields(self, data_dict: dict, is_master: bool = True) -> dict:
                mapped_dict = {}
                for json_key, value in data_dict.items():
                    if not is_master and json_key in DETAIL_SPECIFIC_MAPPING:
                        sql_key = DETAIL_SPECIFIC_MAPPING[json_key]
                    else:
                        sql_key = JSON_TO_SQL_MAPPING.get(json_key, json_key)
                    mapped_dict[sql_key] = value
                return mapped_dict
            
            def _parse_json_input_data(self, raw_data: dict) -> dict:
                parsed = {"master": {}, "detail": [], "other": {}}
                if 'data' in raw_data and isinstance(raw_data['data'], dict):
                    invoice_data = raw_data['data']
                    structure = invoice_data.get('structure', {})
                    invoices = invoice_data.get('invoices', [])
                    master_header = structure.get('master', [])
                    detail_header = structure.get('detail', [])
                    if invoices:
                        master_values = invoices[0].get('master', [])
                        raw_master = dict(zip(master_header, master_values))
                        sql_mapped_master = self._map_json_to_sql_fields(raw_master, is_master=True)
                        parsed['master'] = sql_mapped_master
                        
                        detail_rows = invoices[0].get('detail', [])
                        parsed['detail'] = []
                        for row in detail_rows:
                            raw_detail_row = dict(zip(detail_header, row))
                            sql_mapped_detail = self._map_json_to_sql_fields(raw_detail_row, is_master=False)
                            parsed['detail'].append(sql_mapped_detail)
                            
                    for key, value in raw_data.items():
                        if key != 'data': parsed['other'][key] = value
                return parsed
        
        parser = JsonParser()
        parsed_data = parser._parse_json_input_data(sample_json)
        
        # Tạo model với rules
        sample_rules = [
            {
                "doc_type": "Hóa đơn bán hàng",
                "name": "[Thông tin chung] Tên đơn vị mua hàng không được để trống.",
                "condition": "SELECT 1 FROM master WHERE Tên_đơn_vị_mua_hàng IS NULL OR Tên_đơn_vị_mua_hàng = ''"
            },
            {
                "doc_type": "Hóa đơn bán hàng", 
                "name": "[Chi tiết] Có dòng hàng với số lượng nhỏ hơn hoặc bằng 0.",
                "condition": "SELECT 1 FROM details WHERE Số_lượng <= 0"
            },
            {
                "doc_type": "Hóa đơn bán hàng",
                "name": "[Chi tiết] Có dòng hàng với đơn giá âm.",
                "condition": "SELECT 1 FROM details WHERE Giá < 0"
            },
            {
                "doc_type": "Hóa đơn bán hàng",
                "name": "[Đối chiếu] Tổng tiền hàng chi tiết không khớp với tổng tiền hàng chung.",
                "condition": "SELECT 1 FROM master WHERE ABS(Tổng_tiền_hàng - (SELECT SUM(Tiền_hàng) FROM details)) > 1"
            },
            {
                "doc_type": "Hóa đơn bán hàng",
                "name": "[Chi tiết] Thành tiền của một dòng không bằng (Số lượng * Giá).",
                "condition": "SELECT 1 FROM details WHERE ABS(Tiền_hàng - (Số_lượng * Giá)) > 1"
            }
        ]
        
        model = EInvoiceDebuggerModel(sample_rules)
        
        validation_data = {
            "master": parsed_data.get("master", {}),
            "detail": parsed_data.get("detail", [])
        }
        
        errors = model.validate_data("Hóa đơn bán hàng", validation_data)
        
        print(f"   ✓ Test '{test_name}' hoàn thành")
        print(f"   ✓ Số lỗi phát hiện: {len(errors)}")
        
        if errors:
            print("   📋 Danh sách lỗi:")
            for i, error in enumerate(errors, 1):
                print(f"     {i}. {error}")
            print("   ✅ Hệ thống đã phát hiện lỗi đúng như mong đợi")
        else:
            print("   ⚠️ Không phát hiện lỗi nào (có thể cần kiểm tra lại rules)")
        
        return len(errors) > 0
        
    except Exception as e:
        print(f"   ❌ Lỗi trong quá trình test '{test_name}': {e}")
        return False

def main():
    """Main function"""
    print("Bắt đầu test khả năng phát hiện lỗi...\n")

    try:
        test_error_detection()
        success = True
    except Exception as e:
        print(f"❌ Lỗi tổng quát: {e}")
        success = False

    print(f"\n=== KẾT QUẢ TỔNG QUAN ===")
    print(f"✓ Đã hoàn thành test khả năng phát hiện lỗi")
    print(f"✓ Hệ thống có thể phát hiện các loại lỗi phổ biến")
    print(f"✓ Validation rules hoạt động chính xác")

    if success:
        print("\n🎉 TẤT CẢ TESTS PHÁT HIỆN LỖI ĐỀU THÀNH CÔNG!")
        return True
    else:
        print(f"\n❌ CÓ LỖI XẢY RA TRONG QUÁ TRÌNH TEST!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
