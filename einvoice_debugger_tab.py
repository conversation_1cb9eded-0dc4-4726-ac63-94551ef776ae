import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
from ttkbootstrap.scrolled import ScrolledText
import json
import os
import re
import xml.etree.ElementTree as ET
from xml.dom import minidom
import sqlite3

# Mapping từ JSON field names sang Vietnamese field names cho SQL validation
JSON_TO_SQL_MAPPING = {
    # Master fields mapping
    "Key": "Khóa",
    "InvoiceDate": "Ngày_hóa_đơn", 
    "CustomerCode": "Mã_khách_hàng",
    "Buyer": "Tên_người_mua_hàng",
    "CustomerName": "Tên_đơn_vị_mua_hàng",
    "CustomerTaxCode": "Mã_số_thuế_người_mua",
    "CustomerType": "Loại_khách_hàng",
    "Address": "Địa_chỉ_người_mua_hàng",
    "PhoneNumber": "Điện_thoại_người_mua_hàng",
    "FaxNumber": "Số_fax_người_mua_hàng",
    "EmailDeliver": "Danh_sách_email_người_mua_hàng_nhận_thông_báo_phát_hành",
    "BankAccount": "Tài_khoản_ngân_hàng_người_mua",
    "BankName": "Tên_ngân_hàng_người_mua",
    "PaymentMethod": "Hình_thức_thanh_toán",
    "Currency": "Mã_ngoại_tệ",
    "ExchangeRate": "Tỷ_giá",
    "Amount": "Tổng_tiền_hàng",
    "TotalAmount": "Tổng_tiền_thanh_toán",
    "TaxRate": "Thuế_suất",
    "TaxAmount": "Tổng_tiền_thuế",
    "TaxAmount5": "Tổng_tiền_thuế_5",
    "TaxAmount10": "Tổng_tiền_thuế_10",
    "AmountInWords": "Số_tiền_đọc_bằng_chữ_Tổng_thanh_toán",
    "HumanName": "Tên_người",
    "DiscountAmount": "Tổng_tiền_chiết_khấu",
    "PromotionAmount": "Tổng_tiền_hàng_khuyến_mãi",
    "Note": "Ghi_chú",
    "VoucherType": "Loại_phiếu",
    "IDCardNo": "Số_CMND",
    "PassportNo": "Số_hộ_chiếu",
    "BuyerUnit": "Đơn_vị_người_mua",
    
    # Detail fields mapping - context sensitive
    "ItemCode": "Mã_vật_tư",
    "ItemName": "Tên_vật_tư", 
    "UOM": "Đơn_vị_tính",
    "IsPromotion": "Khuyến_mãi",
    "Quantity": "Số_lượng",
    "Price": "Giá",
    "ProcessType": "Loại_xử_lý"
}

# Context-sensitive mappings for fields that appear in both master and detail
DETAIL_SPECIFIC_MAPPING = {
    "Amount": "Tiền_hàng",  # In detail context
    "TaxAmount": "Tiền_thuế",  # In detail context  
    "DiscountAmount": "Tiền_chiết_khấu"  # In detail context
}

# ===================================================================================
# MODEL - Sử dụng SQL
# ===================================================================================
class EInvoiceDebuggerModel:
    """
    Model quản lý và thực thi các luật kiểm tra lỗi bằng cách sử dụng SQL.
    """
    def __init__(self, initial_rules: list = None):
        self.rules = initial_rules if initial_rules is not None else []

    def set_rules(self, new_rules: list):
        self.rules = new_rules

    def get_rules(self) -> list:
        return self.rules

    def _sanitize_col_name(self, name: str) -> str:
        """Làm sạch tên cột để hợp lệ trong SQL."""
        return ''.join(c if c.isalnum() else '_' for c in name)

    def validate_data(self, doc_type: str, data: dict) -> list[str]:
        """
        Xác thực dữ liệu bằng cách tạo DB trong bộ nhớ và thực thi các câu lệnh SQL.
        """
        errors = []
        master_data = data.get('master', {})
        detail_data = data.get('detail', [])
        
        try:
            conn = sqlite3.connect(':memory:')
            cursor = conn.cursor()
        except sqlite3.Error as e:
            return [f"[Lỗi hệ thống] Không thể tạo DB trong bộ nhớ: {e}"]

        try:
            master_table_created = False
            if master_data:
                master_cols_sanitized = {self._sanitize_col_name(k): v for k, v in master_data.items()}
                master_cols_names = ", ".join(master_cols_sanitized.keys())
                master_placeholders = ", ".join(['?'] * len(master_cols_sanitized))
                
                cursor.execute(f"CREATE TABLE master ({master_cols_names})")
                cursor.execute(f"INSERT INTO master ({master_cols_names}) VALUES ({master_placeholders})", list(master_cols_sanitized.values()))
                master_table_created = True

            details_table_created = False
            if detail_data and detail_data[0]:
                detail_header = [self._sanitize_col_name(k) for k in detail_data[0].keys()]
                detail_cols_names = ", ".join(detail_header)
                detail_placeholders = ", ".join(['?'] * len(detail_header))
                
                cursor.execute(f"CREATE TABLE details ({detail_cols_names})")
                
                rows_to_insert = []
                for row in detail_data:
                    sanitized_row = {self._sanitize_col_name(k): v for k, v in row.items()}
                    rows_to_insert.append([sanitized_row.get(col_name, None) for col_name in detail_header])
                
                cursor.executemany(f"INSERT INTO details ({detail_cols_names}) VALUES ({detail_placeholders})", rows_to_insert)
                details_table_created = True

            for rule in self.rules:
                # *** FIX: Coi doctype rỗng là luật chung ("Tất cả") ***
                rule_doc_type_original = rule.get("doc_type", "Tất cả").strip()
                if not rule_doc_type_original:
                    rule_doc_type_original = "Tất cả"
                
                rule_doc_type = rule_doc_type_original.lower()
                doc_type_to_check = doc_type.lower()
                
                if rule_doc_type != "tất cả" and rule_doc_type != doc_type_to_check:
                    continue

                sql_query = rule.get("condition", "").strip()
                if not sql_query:
                    continue
                
                sql_query_lower = sql_query.lower()
                if 'master' in sql_query_lower and not master_table_created:
                    errors.append(f"[Cảnh báo] Bỏ qua luật '{rule['name']}' vì không có dữ liệu 'master'.")
                    continue
                if 'details' in sql_query_lower and not details_table_created:
                    errors.append(f"[Cảnh báo] Bỏ qua luật '{rule['name']}' vì không có dữ liệu 'details'.")
                    continue

                try:
                    cursor.execute(sql_query)
                    result = cursor.fetchone()
                    if result:
                        errors.append(rule['name'])
                except sqlite3.Error as e:
                    error_msg = str(e).lower()
                    if "no such column" in error_msg:
                        column_name = error_msg.split(':')[-1].strip()
                        errors.append(f"[Lỗi dữ liệu] Luật '{rule['name']}' thất bại do dữ liệu thiếu cột: {column_name}")
                    elif "no such table" in error_msg:
                        table_name = error_msg.split(':')[-1].strip()
                        errors.append(f"[Lỗi dữ liệu] Luật '{rule['name']}' thất bại do dữ liệu thiếu bảng: {table_name}")
                    else:
                        errors.append(f"[Lỗi SQL trong luật '{rule['name']}'] {e}")

        except Exception as e:
            errors.append(f"[Lỗi hệ thống] Lỗi không xác định trong quá trình xác thực: {e}")
        finally:
            conn.close()
            
        return errors


# ===================================================================================
# VIEW & CONTROLLER
# ===================================================================================
class EInvoiceDebuggerTab(ttk.Frame):
    def __init__(self, parent, tooltip_data: dict, field_mapping: dict, doc_types: list, rules: list):
        super().__init__(parent, padding=5)
        
        self.model = EInvoiceDebuggerModel(initial_rules=rules)
        self.tooltip_data = tooltip_data
        self.field_mapping = field_mapping
        self.doc_types = doc_types

        # === CẤU TRÚC LAYOUT CHÍNH (Sử dụng PanedWindow dọc) ===
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        v_pane = ttk.PanedWindow(self, orient=tk.VERTICAL)
        v_pane.grid(row=0, column=0, sticky="nsew")

        top_pane = ttk.PanedWindow(v_pane, orient=tk.HORIZONTAL)
        
        input_frame = ttk.Frame(top_pane, padding=5)
        input_frame.grid_columnconfigure(0, weight=1)
        input_frame.grid_rowconfigure(1, weight=1)
        top_pane.add(input_frame, weight=1) 
        
        doc_type_frame = ttk.Frame(input_frame)
        doc_type_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        ttk.Label(doc_type_frame, text="Loại phiếu:").pack(side="left", padx=(0, 5))
        self.doc_type_var = tk.StringVar()
        
        self.doc_type_combo = ttk.Combobox(doc_type_frame, textvariable=self.doc_type_var, values=self.doc_types, state="readonly")
        self.doc_type_combo.pack(side="left", fill="x", expand=True)
        if self.doc_types:
            self.doc_type_var.set(self.doc_types[0])

        # *** THAY ĐỔI: Gán sự kiện khi thay đổi loại phiếu để lọc lại danh sách luật ***
        self.doc_type_combo.bind("<<ComboboxSelected>>", self._on_doctype_change)

        self.input_notebook = ttk.Notebook(input_frame)
        self.input_notebook.grid(row=1, column=0, sticky="nsew", pady=(5, 0))

        json_tab = ttk.Frame(self.input_notebook, padding=5)
        self.input_notebook.add(json_tab, text="JSON")
        json_tab.grid_rowconfigure(0, weight=1)
        json_tab.grid_columnconfigure(0, weight=1)
        self.json_input_text = ScrolledText(json_tab, wrap=tk.NONE, autohide=True)
        self.json_input_text.grid(row=0, column=0, sticky="nsew")

        xml_tab = ttk.Frame(self.input_notebook, padding=5)
        self.input_notebook.add(xml_tab, text="XML")
        xml_tab.grid_rowconfigure(0, weight=1)
        xml_tab.grid_columnconfigure(0, weight=1)
        self.xml_input_text = ScrolledText(xml_tab, wrap=tk.NONE, autohide=True)
        self.xml_input_text.grid(row=0, column=0, sticky="nsew")

        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=2, column=0, sticky="w", pady=(10, 0))
        self.format_button = ttk.Button(button_frame, text="Định dạng", command=self.format_input, bootstyle="secondary")
        self.format_button.pack(side="left", padx=(0, 10))
        self.analyze_button = ttk.Button(button_frame, text="2. Phân tích", command=self.handle_analyze_einvoice, bootstyle="success")
        self.analyze_button.pack(side="left")

        right_frame = ttk.Frame(top_pane, padding=(5, 0, 0, 0))
        right_frame.grid_rowconfigure(0, weight=1)
        right_frame.grid_columnconfigure(0, weight=1)
        top_pane.add(right_frame, weight=1)
        
        notebook = ttk.Notebook(right_frame)
        notebook.pack(fill="both", expand=True)
        
        error_display_frame = ttk.Frame(notebook, padding=5)
        notebook.add(error_display_frame, text="Kết quả kiểm tra")
        self.error_display = ScrolledText(error_display_frame, wrap=tk.WORD, autohide=True)
        self.error_display.pack(expand=True, fill="both")
        
        self._create_rule_manager_tab(notebook)

        bottom_pane_container = ttk.LabelFrame(v_pane, text="Thông tin hóa đơn", padding=5)
        bottom_pane_container.grid_rowconfigure(0, weight=1)
        bottom_pane_container.grid_columnconfigure(0, weight=1)
        
        info_h_pane = ttk.PanedWindow(bottom_pane_container, orient=tk.HORIZONTAL)
        info_h_pane.grid(row=0, column=0, sticky="nsew")
        
        master_frame = ttk.Frame(info_h_pane, padding=5)
        master_frame.grid_rowconfigure(1, weight=1)
        master_frame.grid_columnconfigure(0, weight=1)
        info_h_pane.add(master_frame, weight=1) 
        self.master_label = ttk.Label(master_frame, text="Thông tin chung (Master) & khác")
        self.master_label.grid(row=0, column=0, sticky="w")
        self.master_view = ttk.Treeview(master_frame, show="headings", columns=("field", "value"), height=5)
        self.master_view.grid(row=1, column=0, sticky="nsew", pady=5)
        
        detail_frame = ttk.Frame(info_h_pane, padding=5)
        detail_frame.grid_rowconfigure(1, weight=1)
        detail_frame.grid_columnconfigure(0, weight=1)
        info_h_pane.add(detail_frame, weight=2) 
        self.detail_label = ttk.Label(detail_frame, text="Chi tiết (Detail):")
        self.detail_label.grid(row=0, column=0, sticky="w")
        self.detail_view = ttk.Treeview(detail_frame, show="headings", height=5)
        self.detail_view.grid(row=1, column=0, sticky="nsew", pady=5)

        try:
            info_h_pane.paneconfigure(master_frame, minsize=160)
            info_h_pane.paneconfigure(detail_frame, minsize=200)
        except Exception: pass

        def _apply_initial_hsplit():
            total_width = info_h_pane.winfo_width()
            if total_width <= 1:
                self.after(100, _apply_initial_hsplit)
                return
            try: info_h_pane.sashpos(0, int(total_width * (1/3)))
            except Exception: pass
        self.after(0, _apply_initial_hsplit)
        
        v_pane.add(top_pane, weight=1)
        v_pane.add(bottom_pane_container, weight=1)

        try:
            v_pane.paneconfigure(top_pane, minsize=140)
            v_pane.paneconfigure(bottom_pane_container, minsize=140)
        except Exception: pass

        def _apply_initial_vsplit():
            total_height = v_pane.winfo_height()
            if total_height <= 1:
                self.after(100, _apply_initial_vsplit)
                return
            try: v_pane.sashpos(0, int(total_height * 0.5))
            except Exception: pass
        self.after(0, _apply_initial_vsplit)

        status_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        status_frame.grid(row=1, column=0, sticky="ew")
        status_frame.grid_columnconfigure(0, weight=1)

        self.status_line1 = ttk.Label(status_frame, text="", anchor="w", bootstyle="primary")
        self.status_line1.pack(fill="x")
        self.status_line2 = ttk.Label(status_frame, text="", anchor="w", bootstyle="primary")
        self.status_line2.pack(fill="x")
        self.status_line3 = ttk.Label(status_frame, text="", anchor="w", bootstyle="primary")
        self.status_line3.pack(fill="x")

        self._setup_treeviews()
        self.clear_guidance()
        self.clear_error_log()
        self._load_rules_to_view()

        self.master_view.bind('<Motion>', self.update_guidance)
        self.master_view.bind('<Leave>', self.clear_guidance)
        self.detail_view.bind('<Motion>', self.update_guidance)
        self.detail_view.bind('<Leave>', self.clear_guidance)
        self.rule_tree.bind('<Leave>', self.clear_guidance)

        
    def _map_display_field_name(self, doc_type_lower: str, level: str, original_key: str) -> str:
        """Trả về tên hiển thị cho cột/field theo đúng cấp (level). Nếu không tìm thấy, trả về key gốc.
        Ưu tiên tra trong `tooltip_data[doctype][level]` theo json_tag/xml_tag để tránh nhầm giữa master/detail.
        """
        # Chuyển level thành dạng viết hoa để khớp với dữ liệu trong file
        level_capitalized = level.capitalize()  # master -> Master, detail -> Detail

        # Tra cứu trong tooltip theo đúng cấp
        level_info = self.tooltip_data.get(doc_type_lower, {}).get(level_capitalized, {})

        # Trước tiên, kiểm tra xem original_key có phải là tên SQL đã được map không
        # Nếu có, tìm ngược lại JSON tag gốc
        json_key_from_sql = None
        for json_key, sql_key in JSON_TO_SQL_MAPPING.items():
            if sql_key == original_key:
                json_key_from_sql = json_key
                break

        # Nếu không phải detail context, kiểm tra DETAIL_SPECIFIC_MAPPING
        if not json_key_from_sql and level == 'detail':
            for json_key, sql_key in DETAIL_SPECIFIC_MAPPING.items():
                if sql_key == original_key:
                    json_key_from_sql = json_key
                    break

        # Tìm content_name dựa trên json_tag hoặc xml_tag
        search_keys = [json_key_from_sql, original_key] if json_key_from_sql else [original_key]

        for content_name, info in level_info.items():
            try:
                if isinstance(info, dict):
                    json_tag = info.get('json_tag', '')
                    xml_tag = info.get('xml_tag', '')
                    if any(key in [json_tag, xml_tag] for key in search_keys if key):
                        return content_name
            except (AttributeError, TypeError):
                continue

        # Không tìm thấy thì trả về tên gốc
        return original_key

    def _build_level_mapping(self, doc_type_lower: str, level: str) -> dict:
        """Tạo mapping tag (json/xml) -> content_name cho từng cấp (master/detail)."""
        level_info = self.tooltip_data.get(doc_type_lower, {}).get(level, {})
        mapping: dict = {}
        for content_name, info in level_info.items():
            if not isinstance(info, dict):
                continue
            json_tag = info.get('json_tag')
            xml_tag = info.get('xml_tag')
            if json_tag:
                mapping[str(json_tag)] = content_name
            if xml_tag:
                mapping[str(xml_tag)] = content_name
        return mapping


    def update_all_data(self, tooltip_data: dict, field_mapping: dict, doc_types: list, rules: list):
        """
        Cập nhật tất cả dữ liệu động (định nghĩa, luật,...) và làm mới giao diện.
        """
        self.tooltip_data = tooltip_data
        self.field_mapping = field_mapping
        self.doc_types = doc_types
        self.model.set_rules(rules)

        current_selection = self.doc_type_var.get()
        self.doc_type_combo['values'] = self.doc_types
        if current_selection in self.doc_types:
            self.doc_type_var.set(current_selection)
        elif self.doc_types:
            self.doc_type_var.set(self.doc_types[0])
        else:
            self.doc_type_var.set("")

        self._load_rules_to_view()
        
        self.clear_error_log()
        self.update_master_grid({})
        self.update_detail_grid([])
        self.clear_guidance()

    def _check_sql_rule_validity(self, sql_query: str, doc_type: str) -> tuple[bool, str]:
        """
        Kiểm tra cú pháp và sự hợp lệ của câu lệnh SQL bằng cách tạo một schema DB tạm thời.
        """
        if not sql_query.strip():
            return True, "Hợp lệ (trống)"

        doc_type_lower = doc_type.lower()
        if doc_type_lower not in self.tooltip_data:
            return (False, f"Lỗi: Không tìm thấy định nghĩa cho loại phiếu '{doc_type}'")

        try:
            conn = sqlite3.connect(':memory:')
            cursor = conn.cursor()

            # Luôn tạo bảng master và details với các cột cơ bản
            # Tạo bảng master với các cột từ JSON_TO_SQL_MAPPING
            master_cols = list(JSON_TO_SQL_MAPPING.values())
            master_cols_sanitized = [self.model._sanitize_col_name(col) for col in master_cols]
            cursor.execute(f"CREATE TABLE master ({', '.join([f'{col} TEXT' for col in master_cols_sanitized])})")

            # Tạo bảng details với các cột từ DETAIL_SPECIFIC_MAPPING và một số cột chung
            detail_cols = list(DETAIL_SPECIFIC_MAPPING.values()) + ["Mã_vật_tư", "Tên_vật_tư", "Đơn_vị_tính", "Số_lượng", "Giá"]
            detail_cols_sanitized = [self.model._sanitize_col_name(col) for col in detail_cols]
            cursor.execute(f"CREATE TABLE details ({', '.join([f'{col} TEXT' for col in detail_cols_sanitized])})")

            # Thêm các cột từ tooltip_data nếu có (sử dụng level viết hoa)
            master_fields = self.tooltip_data.get(doc_type_lower, {}).get('Master', {})
            detail_fields = self.tooltip_data.get(doc_type_lower, {}).get('Detail', {})

            # Thêm cột master từ tooltip nếu chưa có
            for field_name in master_fields.keys():
                sanitized_name = self.model._sanitize_col_name(field_name)
                if sanitized_name not in master_cols_sanitized:
                    try:
                        cursor.execute(f"ALTER TABLE master ADD COLUMN {sanitized_name} TEXT")
                    except sqlite3.OperationalError:
                        pass  # Cột đã tồn tại

            # Thêm cột detail từ tooltip nếu chưa có
            for field_name in detail_fields.keys():
                sanitized_name = self.model._sanitize_col_name(field_name)
                if sanitized_name not in detail_cols_sanitized:
                    try:
                        cursor.execute(f"ALTER TABLE details ADD COLUMN {sanitized_name} TEXT")
                    except sqlite3.OperationalError:
                        pass  # Cột đã tồn tại

            cursor.execute(f"EXPLAIN QUERY PLAN {sql_query}")

            conn.close()
            return (True, "Hợp lệ")

        except sqlite3.Error as e:
            return (False, f"KHÔNG HỢP LỆ: {e}")

    def _create_rule_manager_tab(self, parent_notebook):
        manager_frame = ttk.Frame(parent_notebook, padding=5)
        parent_notebook.add(manager_frame, text="Danh sách luật")

        manager_frame.grid_rowconfigure(0, weight=1)
        manager_frame.grid_columnconfigure(0, weight=1)

        self.rule_tree = ttk.Treeview(manager_frame, columns=("name",), show="headings")
        self.rule_tree.grid(row=0, column=0, sticky="nsew")
        self.rule_tree.heading("name", text="Tên lỗi")
        self.rule_tree.column("name", width=400)
        
        self.rule_tree.bind("<<TreeviewSelect>>", self._on_rule_select)

    def _on_doctype_change(self, event=None):
        """Xử lý sự kiện khi loại phiếu thay đổi: tải lại danh sách luật và xóa hướng dẫn."""
        self._load_rules_to_view()
        self.clear_guidance()

    def _on_rule_select(self, event=None):
        selected_items = self.rule_tree.selection()
        if not selected_items:
            return

        selected_item = selected_items[0]
        item_values = self.rule_tree.item(selected_item, "values")
        doc_type_to_check = self.doc_type_var.get()

        if not doc_type_to_check:
            self._update_status_bar("Vui lòng chọn một loại phiếu để kiểm tra luật.", "", "")
            return

        if item_values and len(item_values) > 2:
            name, doc_type_rule, condition = item_values[0], item_values[1], item_values[2]
            
            applies_to_current = (doc_type_rule.lower() == 'tất cả' or doc_type_rule.lower() == doc_type_to_check.lower())
            
            is_valid, syntax_status = self._check_sql_rule_validity(condition, doc_type_to_check)
            
            final_status = f"Trạng thái: {syntax_status}"
            if not applies_to_current:
                final_status += f" (Lưu ý: Luật này không áp dụng cho loại phiếu '{doc_type_to_check}' đang chọn)"

            self._update_status_bar(
                f"Loại áp dụng của luật: {doc_type_rule}",
                f"Câu lệnh SQL: {condition}",
                final_status
            )

    def _load_rules_to_view(self):
        """
        Tải các luật vào treeview, được lọc theo loại phiếu đang được chọn.
        Các luật có doctype là 'Tất cả' hoặc rỗng sẽ luôn được hiển thị.
        """
        self.rule_tree.delete(*self.rule_tree.get_children())
        selected_doctype = self.doc_type_var.get()
        if not selected_doctype:
            return

        selected_doctype_lower = selected_doctype.lower()

        for rule in self.model.get_rules():
            rule_doctype_original = rule.get("doc_type", "Tất cả").strip()
            if not rule_doctype_original:
                rule_doctype_original = "Tất cả"
            
            rule_doctype_lower = rule_doctype_original.lower()

            if rule_doctype_lower == "tất cả" or rule_doctype_lower == selected_doctype_lower:
                self.rule_tree.insert("", "end", values=(
                    rule.get("name", "N/A"),
                    rule_doctype_original,
                    rule.get("condition", "N/A")
                ))

    def handle_analyze_einvoice(self):
        self.clear_error_log()
        
        try:
            selected_tab_text = self.input_notebook.tab(self.input_notebook.select(), "text")
            input_format = selected_tab_text.upper()
        except tk.TclError:
            messagebox.showerror("Lỗi", "Vui lòng chọn một tab (JSON hoặc XML).")
            return
        
        if input_format == "JSON":
            input_string = self.json_input_text.get("1.0", tk.END)
        else: # XML
            input_string = self.xml_input_text.get("1.0", tk.END)

        if not input_string.strip():
            messagebox.showerror("Lỗi", "Vui lòng dán dữ liệu đầu vào.")
            return

        doc_type_original = self.doc_type_var.get()
        if not doc_type_original:
            messagebox.showerror("Lỗi", "Vui lòng chọn một loại phiếu.")
            return
        doc_type_lower = doc_type_original.lower()

        try:
            if input_format == "JSON":
                raw_data = json.loads(input_string)
                parsed_data = self._parse_json_input_data(raw_data)
            elif input_format == "XML":
                parsed_data = self._parse_xml_input_data(input_string)
            
            # Dữ liệu đã được ánh xạ đúng cấp trong _parse_json_input_data, không map lại theo field_mapping tổng thể
            validation_data = {
                "master": parsed_data.get("master", {}),
                "detail": parsed_data.get("detail", [])
            }
            
            master_and_other_data = validation_data["master"].copy()
            master_and_other_data.update(parsed_data.get("other", {}))
            
            display_data = {
                "master": master_and_other_data,
                "detail": validation_data["detail"],
            }

            errors = self.model.validate_data(doc_type_original, validation_data)

            if errors:
                self.log_error("\n".join(errors))
            else:
                self.log_error("Không tìm thấy lỗi nào theo các luật đã khai báo.", is_error=False)

            self.update_master_grid(display_data.get("master", {}))
            self.update_detail_grid(display_data.get("detail", []))

        except json.JSONDecodeError:
            self.log_error("Dữ liệu không phải là định dạng JSON hợp lệ.")
            messagebox.showerror("Lỗi JSON", "Dữ liệu không phải là định dạng JSON hợp lệ.")
        except ET.ParseError:
            self.log_error("Dữ liệu không phải là định dạng XML hợp lệ.")
            messagebox.showerror("Lỗi XML", "Dữ liệu không phải là định dạng XML hợp lệ.")
        except Exception as e:
            self.log_error(f"Đã xảy ra lỗi không xác định: {e}")
            messagebox.showerror("Lỗi không xác định", f"Đã xảy ra lỗi không xác định: {e}")

    def _map_json_to_sql_fields(self, data_dict: dict, is_master: bool = True) -> dict:
        """
        Map JSON field names to Vietnamese SQL field names for validation
        
        Args:
            data_dict: Dictionary with JSON field names as keys
            is_master: True for master data, False for detail data
            
        Returns:
            Dictionary with Vietnamese field names as keys
        """
        mapped_dict = {}
        for json_key, value in data_dict.items():
            # For detail context, check detail-specific mappings first
            if not is_master and json_key in DETAIL_SPECIFIC_MAPPING:
                sql_key = DETAIL_SPECIFIC_MAPPING[json_key]
            else:
                # Try to find mapping, fallback to original key if not found
                sql_key = JSON_TO_SQL_MAPPING.get(json_key, json_key)
            mapped_dict[sql_key] = value
        return mapped_dict

    def _parse_json_input_data(self, raw_data: dict) -> dict:
        parsed = {"master": {}, "detail": [], "other": {}}
        if 'data' in raw_data and isinstance(raw_data['data'], dict):
            invoice_data = raw_data['data']
            structure = invoice_data.get('structure', {})
            invoices = invoice_data.get('invoices', [])
            master_header = structure.get('master', [])
            detail_header = structure.get('detail', [])
            if invoices:
                master_values = invoices[0].get('master', [])
                
                # Create raw master data first
                raw_master = dict(zip(master_header, master_values))
                
                # Map to Vietnamese field names for SQL validation
                sql_mapped_master = self._map_json_to_sql_fields(raw_master, is_master=True)
                parsed['master'] = sql_mapped_master
                
                detail_rows = invoices[0].get('detail', [])
                parsed['detail'] = []
                for row in detail_rows:
                    raw_detail_row = dict(zip(detail_header, row))
                    # Map to Vietnamese field names for SQL validation
                    sql_mapped_detail = self._map_json_to_sql_fields(raw_detail_row, is_master=False)
                    parsed['detail'].append(sql_mapped_detail)
                    
            for key, value in raw_data.items():
                if key != 'data': parsed['other'][key] = value
        elif 'master' in raw_data and 'detail' in raw_data:
            # Map existing master/detail data to SQL field names
            raw_master = raw_data.get('master', {})
            parsed['master'] = self._map_json_to_sql_fields(raw_master, is_master=True)
            
            raw_detail = raw_data.get('detail', [])
            parsed['detail'] = []
            for row in raw_detail:
                sql_mapped_detail = self._map_json_to_sql_fields(row, is_master=False)
                parsed['detail'].append(sql_mapped_detail)
                
            for key, value in raw_data.items():
                if key not in ['master', 'detail']: parsed['other'][key] = value
        else:
            parsed['other'] = raw_data
        return parsed

    def _get_xml_text(self, element, path, default=''):
        node = element.find(path)
        return node.text.strip() if node is not None and node.text else default

    def _parse_xml_input_data(self, xml_string: str) -> dict:
        root = ET.fromstring(xml_string)
        master, detail, other = {}, [], {}

        def add_children_to_dict(parent_element, target_dict):
            if parent_element is None: return
            for child in parent_element:
                child_tag = child.tag
                if child_tag == 'TTKhac':
                    for ttin in child.findall('TTin'):
                        truong, dlieu = self._get_xml_text(ttin, 'TTruong'), self._get_xml_text(ttin, 'DLieu')
                        if truong:
                            target_dict[truong] = dlieu
                elif child.text:
                    target_dict[child_tag] = child.text.strip()

        if (mccqt_node := root.find('MCCQT')) is not None:
            other['MCCQT'] = mccqt_node.text
        add_children_to_dict(root.find('.//TTChung'), master)
        add_children_to_dict(root.find('.//NBan'), master)
        add_children_to_dict(root.find('.//NMua'), master)
        
        if (ttoan := root.find('.//TToan')) is not None:
            for child in ttoan:
                if child.tag == 'THTTLTSuat':
                    for i, ltsuat in enumerate(child.findall('LTSuat')):
                        master[f'LTSuat_{i+1}_TSuat'] = self._get_xml_text(ltsuat, 'TSuat')
                        master[f'LTSuat_{i+1}_ThTien'] = self._get_xml_text(ltsuat, 'ThTien')
                        master[f'LTSuat_{i+1}_TThue'] = self._get_xml_text(ltsuat, 'TThue')
                elif child.text:
                    master[child.tag] = child.text.strip()

        if (dshhdvu := root.find('.//DSHHDVu')) is not None:
            for hhdvu_node in dshhdvu.findall('HHDVu'):
                detail_item = {}
                add_children_to_dict(hhdvu_node, detail_item)
                detail.append(detail_item)

        return {"master": master, "detail": detail, "other": other}

    def format_input(self):
        try:
            selected_tab_text = self.input_notebook.tab(self.input_notebook.select(), "text")
            input_format = selected_tab_text.upper()

            active_text_widget = self.json_input_text if input_format == "JSON" else self.xml_input_text
            current_text = active_text_widget.get("1.0", tk.END)
            if not current_text.strip(): return
            
            if input_format == "JSON":
                parsed_json = json.loads(current_text)
                formatted_text = json.dumps(parsed_json, indent=4, ensure_ascii=False)
            else: # XML
                reparsed = minidom.parseString(current_text)
                pretty_xml = reparsed.toprettyxml(indent="  ")
                formatted_text = "\n".join([line for line in pretty_xml.split('\n') if line.strip()])

            active_text_widget.delete("1.0", tk.END)
            active_text_widget.insert("1.0", formatted_text)
        except json.JSONDecodeError:
            messagebox.showerror("Lỗi JSON", "Nội dung không phải là định dạng JSON hợp lệ.")
        except Exception as e:
            messagebox.showerror(f"Lỗi Định dạng", f"Không thể định dạng nội dung.\nLỗi: {e}")

    def update_guidance(self, event):
        widget = event.widget
        doc_type_original = self.doc_type_var.get()
        if not doc_type_original: return
        doc_type_lower = doc_type_original.lower()

        level = 'master' if widget == self.master_view else 'detail'

        field_name = ""
        if level == 'master':
            item_id = widget.identify_row(event.y)
            if item_id and (values := widget.item(item_id, 'values')):
                field_name = values[0]  # Đây là tên hiển thị
        elif level == 'detail':
            if widget.identify_region(event.x, event.y) == "heading":
                column_id = widget.identify_column(event.x)
                field_name = widget.heading(column_id, 'text')  # Đây là tên hiển thị

        if field_name:
            # Chuyển level thành dạng viết hoa để khớp với dữ liệu trong file
            level_capitalized = level.capitalize()  # master -> Master, detail -> Detail

            # Tìm field_info dựa trên tên hiển thị (content_name)
            level_info = self.tooltip_data.get(doc_type_lower, {}).get(level_capitalized, {})
            field_info = level_info.get(field_name)

            # Nếu không tìm thấy bằng tên hiển thị, thử tìm bằng json_tag/xml_tag
            if not field_info:
                for content_name, info in level_info.items():
                    if isinstance(info, dict):
                        json_tag = info.get('json_tag', '')
                        xml_tag = info.get('xml_tag', '')
                        if field_name in [json_tag, xml_tag]:
                            field_info = info
                            break

            if field_info and isinstance(field_info, dict):
                desc = field_info.get('desc', "N/A")
                print_name = field_info.get('print_name', "N/A")
                json_tag = field_info.get('json_tag', "N/A")
                xml_tag = field_info.get('xml_tag', "N/A")

                line1_text = f"Trường JSON: {json_tag} | Trường XML: {xml_tag} | Biến mẫu in: {print_name}"
                description_text = desc if isinstance(desc, str) else str(desc)
                max_len = 120
                if len(description_text) > max_len:
                    split_idx = description_text.rfind(' ', 0, max_len)
                    if split_idx == -1: split_idx = max_len
                    part1, part2 = description_text[:split_idx], description_text[split_idx:].lstrip()
                    line2_text, line3_text = f"Nội dung: {part1}", part2
                else:
                    line2_text, line3_text = f"Nội dung: {description_text}", ""

                self._update_status_bar(line1_text, line2_text, line3_text)
            else:
                self.clear_guidance(message=f"Không tìm thấy thông tin chi tiết cho trường '{field_name}'.")
        else:
            self.clear_guidance()

    def clear_guidance(self, event=None, message=None):
        if message:
            line1, line2, line3 = message, "", ""
        else:
            line1 = "Sử dụng SQL để khai báo luật. Bảng có sẵn: master, details."
            line2 = "Di chuột vào trường để xem thông tin chi tiết (tên cột trong SQL có thể khác)."
            line3 = "Nếu câu lệnh SQL trả về kết quả, lỗi sẽ được ghi nhận."
        self._update_status_bar(line1, line2, line3)

    def _update_status_bar(self, line1: str, line2: str, line3: str):
        self.status_line1.config(text=line1)
        self.status_line2.config(text=line2)
        self.status_line3.config(text=line3)

    def _setup_treeviews(self):
        self.master_view.heading("field", text="Trường"); self.master_view.column("field", width=150, stretch=False)
        self.master_view.heading("value", text="Giá trị"); self.master_view.column("value", stretch=True)

    def log_error(self, message: str, is_error: bool = True):
        self.error_display.text.config(state='normal')
        self.error_display.delete("1.0", tk.END)
        self.error_display.insert(tk.END, message)
        self.error_display.text.config(state='disabled')

    def clear_error_log(self):
        self.log_error("Sẵn sàng để kiểm tra...", is_error=False)

    def update_master_grid(self, data: dict):
        self.master_view.delete(*self.master_view.get_children())
        doc_type_original = self.doc_type_var.get()
        doc_type_lower = doc_type_original.lower() if doc_type_original else ""
        for key, value in data.items():
            display_key = self._map_display_field_name(doc_type_lower, 'master', key) if doc_type_lower else key
            self.master_view.insert("", "end", values=(display_key, value))
            
    def update_detail_grid(self, data_rows: list):
        self.detail_view.delete(*self.detail_view.get_children())
        if not data_rows:
            self.detail_view["columns"] = []
            return
        
        doc_type_original = self.doc_type_var.get()
        doc_type_lower = doc_type_original.lower() if doc_type_original else ""
        header = list(data_rows[0].keys()) if data_rows else []
        self.detail_view["columns"] = header
        for col in header:
            display_col = self._map_display_field_name(doc_type_lower, 'detail', col) if doc_type_lower else col
            self.detail_view.heading(col, text=display_col)
            self.detail_view.column(col, width=120)
        for row_data in data_rows:
            self.detail_view.insert("", "end", values=[row_data.get(h, '') for h in header])
