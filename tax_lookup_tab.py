# tax_lookup_tab.py
import ttkbootstrap as tkb
from ttkbootstrap.constants import *
import tkinter as tk
from tkinter import ttk
from tkinter import filedialog, messagebox, simpledialog
import requests
import csv
import re
import unicodedata
import os
import threading
import sqlite3
from concurrent.futures import ThreadPoolExecutor, as_completed, CancelledError
from datetime import datetime, timedelta
import sys
import subprocess

# Cố gắng import openpyxl để kiểm tra sự tồn tại
try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

# ===================================================================================
# CONSTANTS
# ===================================================================================
ADDR_PENDING = "Đang chờ"
ADDR_VALID = "Hợp lệ"
ADDR_INVALID_PROVINCE = "Sai Tỉnh/TP"
ADDR_INVALID_WARD = "Sai Phường/Xã"
ADDR_INCOMPLETE = "Thiếu Tỉnh/Xã"
ADDR_NO_DIRECTORY = "Chưa có DM"
ADDR_NOT_FOUND = "N/A"

STATUS_MAP = {
    "00": "NNT đã được cấp MST", "01": "NNT ngừng hoạt động và đã hoàn thành thủ tục chấm dứt hiệu lực MST",
    "02": "NNT đã chuyển cơ quan thuế quản lý", "03": "NNT ngừng hoạt động nhưng chưa hoàn thành thủ tục chấm dứt hiệu lực MST",
    "04": "NNT đang hoạt động (áp dụng cho hộ kinh doanh, cá nhân kinh doanh chưa đủ thông tin đăng ký thuế)",
    "05": "NNT tạm ngừng hoạt động, kinh doanh", "06": "NNT không hoạt động tại địa chỉ đã đăng ký",
    "07": "NNT chờ làm thủ tục phá sản"
}

requests.packages.urllib3.disable_warnings(requests.packages.urllib3.exceptions.InsecureRequestWarning)

def normalize_str(s):
    if not isinstance(s, str): return ""
    s_norm = ''.join(c for c in unicodedata.normalize('NFD', s.strip().lower()) if unicodedata.category(c) != 'Mn')
    prefixes = [r'^(thanh pho|tp|tinh|t|phuong|p|xa|x)\.?\s+']
    for p in prefixes: s_norm = re.sub(p, '', s_norm, count=1)
    return re.sub(r'\s+', ' ', s_norm).strip()

# ===================================================================================
# MODEL
# ===================================================================================
class TaxLookupModel:
    def __init__(self, db_name="tax_data.db"):
        self.db_name = db_name
        self.local = threading.local()
        self._create_table()

    def _get_conn(self):
        if not hasattr(self.local, 'conn'):
            self.local.conn = sqlite3.connect(self.db_name, timeout=10, check_same_thread=False)
            self.local.conn.row_factory = sqlite3.Row
            self.local.conn.execute("PRAGMA journal_mode=WAL;")
        return self.local.conn

    def _create_table(self):
        conn = self._get_conn()
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tax_data (
                mst TEXT PRIMARY KEY, ten TEXT, tthai TEXT, dctsdchi TEXT,
                dctstxa TEXT, dctstinh TEXT, diachi_daydu TEXT,
                diachi_hop_le TEXT, last_updated TEXT
            )
        """)
        conn.commit()

    def add_msts(self, msts):
        conn = self._get_conn()
        cursor = conn.cursor()
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        data_to_insert = [(mst, 'Đang chờ...', 'Đang chờ', now) for mst in msts]
        cursor.executemany("INSERT OR IGNORE INTO tax_data (mst, ten, diachi_hop_le, last_updated) VALUES (?, ?, ?, ?)", data_to_insert)
        conn.commit()
        return cursor.rowcount

    def batch_update_records(self, records_data):
        if not records_data: return
        conn = self._get_conn()
        cursor = conn.cursor()
        sql = """
            UPDATE tax_data SET
                ten = :ten, tthai = :tthai, dctsdchi = :dctsdchi, dctstxa = :dctstxa,
                dctstinh = :dctstinh, diachi_daydu = :diachi_daydu,
                diachi_hop_le = :diachi_hop_le, last_updated = :last_updated
            WHERE mst = :mst
        """
        try:
            cursor.executemany(sql, records_data)
            conn.commit()
        except Exception as e:
            print(f"Lỗi khi cập nhật hàng loạt, đang rollback: {e}")
            conn.rollback()

    def get_all_records(self):
        conn = self._get_conn()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM tax_data ORDER BY last_updated DESC")
        return cursor.fetchall()

    def get_records_to_lookup(self):
        conn = self._get_conn()
        cursor = conn.cursor()
        cursor.execute("SELECT mst FROM tax_data WHERE ten = 'Đang chờ...'")
        return [row['mst'] for row in cursor.fetchall()]

    def get_outdated_records(self, days):
        conn = self._get_conn()
        cursor = conn.cursor()
        cutoff_date = datetime.now() - timedelta(days=days)
        cutoff_date_str = cutoff_date.strftime("%Y-%m-%d %H:%M:%S")
        cursor.execute("SELECT mst FROM tax_data WHERE last_updated < ? OR ten = 'Đang chờ...'", (cutoff_date_str,))
        return [row['mst'] for row in cursor.fetchall()]
    
    def delete_records_by_mst(self, msts):
        if not msts: return 0
        conn = self._get_conn()
        cursor = conn.cursor()
        data_to_delete = [(mst,) for mst in msts]
        cursor.executemany("DELETE FROM tax_data WHERE mst = ?", data_to_delete)
        conn.commit()
        return cursor.rowcount

    def close_connection(self):
        if hasattr(self.local, 'conn'):
            self.local.conn.close()
            del self.local.conn

# ===================================================================================
# VIEW
# ===================================================================================
class TaxLookupView(ttk.Frame):
    def __init__(self, parent, controller):
        super().__init__(parent)
        self.controller = controller
        self.style = tkb.Style.get_instance()
        self.filter_vars = {}
        self.update_ten_var = tk.BooleanVar(value=True)
        self.update_diachi_var = tk.BooleanVar(value=True)
        self.num_threads_var = tk.IntVar(value=10)
        self.original_status_text = ""
        self._after_id = None
        self._create_widgets()
        self.after(100, self._sync_filter_widths)

    def _create_widgets(self):
        self.pack(fill=BOTH, expand=True)
        top_frame = tkb.Frame(self, padding=(10, 10, 10, 0))
        top_frame.pack(fill=X, side=TOP)
        self._create_buttons(top_frame)
        output_frame = tkb.Frame(self, padding=10)
        output_frame.pack(fill=BOTH, expand=True, side=TOP)
        instruction_label = tkb.Label(output_frame, text="Nhập vào các ô bên dưới để lọc dữ liệu. Dán (Ctrl+V) vào bảng để thêm MST.", font=("Helvetica", 10, "italic"))
        instruction_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        self._create_filter_header(output_frame)
        self._create_treeview(output_frame)
        self._create_status_bar()

    def _create_buttons(self, parent):
        btn_frame = tkb.Frame(parent)
        btn_frame.pack(side=LEFT)
        self.lookup_btn = tkb.Button(btn_frame, text="Tra cứu", command=self.controller.start_lookup_pending, bootstyle="success")
        self.lookup_btn.pack(side=LEFT, padx=(0, 5))
        self.relookup_btn = tkb.Button(btn_frame, text="Tra lại", command=self.controller.start_relookup_old, bootstyle="success-outline")
        self.relookup_btn.pack(side=LEFT, padx=5)
        self.import_btn = tkb.Button(btn_frame, text="Import", command=self.controller.import_from_file, bootstyle="primary")
        self.import_btn.pack(side=LEFT, padx=5)
        self.delete_btn = tkb.Button(btn_frame, text="Xóa", command=self.controller.delete_filtered_records, bootstyle="danger")
        self.delete_btn.pack(side=LEFT, padx=5)
        self.export_btn = tkb.Button(btn_frame, text="Xuất Excel", command=self.controller.export_to_excel, bootstyle="info")
        self.export_btn.pack(side=LEFT, padx=5)
        tkb.Separator(btn_frame, orient=VERTICAL).pack(side=LEFT, padx=10, fill='y', pady=5)
        self._create_sql_options_dropdown(btn_frame)
        self.gen_sql_btn = tkb.Button(btn_frame, text="Gen SQL", command=self.controller.generate_sql, bootstyle="secondary")
        self.gen_sql_btn.pack(side=LEFT, padx=5)
        tkb.Separator(btn_frame, orient=VERTICAL).pack(side=LEFT, padx=10, fill='y', pady=5)
        thread_label = tkb.Label(btn_frame, text="Luồng:")
        thread_label.pack(side=LEFT, padx=(5, 2))
        self.thread_spinbox = tkb.Spinbox(btn_frame, from_=1, to=50, textvariable=self.num_threads_var, width=5)
        self.thread_spinbox.pack(side=LEFT)

    def _create_filter_header(self, parent):
        self.filter_frame = tkb.Frame(parent)
        self.filter_frame.grid(row=1, column=0, sticky="ew", pady=(0, 2))
        col_info = self._get_column_info()
        for i, (col_id, config) in enumerate(col_info.items()):
            var = tk.StringVar()
            var.trace_add("write", self.on_filter_change_debounced)
            self.filter_vars[col_id] = var
            entry = tkb.Entry(self.filter_frame, textvariable=var, font=("Helvetica", 9))
            entry.grid(row=0, column=i, sticky="ew", padx=(0, 4))
            self.filter_frame.grid_columnconfigure(i, weight=1, minsize=config["width"]-10)

    def _get_column_info(self):
        return {
            "mst": {"text": "Mã số thuế", "width": 100}, "ten": {"text": "Tên người nộp thuế", "width": 250},
            "tthai": {"text": "Trạng thái NNT", "width": 260}, "diachi_hop_le": {"text": "Trạng thái địa chỉ", "width": 120},
            "dctstinh": {"text": "Tỉnh/Thành phố", "width": 150}, "dctstxa": {"text": "Phường/Xã", "width": 150},
            "dctsdchi": {"text": "Số nhà/Đường", "width": 180}, "last_updated": {"text": "Cập nhật", "width": 130}
        }

    def _create_treeview(self, parent):
        all_columns = tuple(self._get_column_info().keys()) + ("diachi_daydu",)
        display_columns = tuple(self._get_column_info().keys())
        self.result_tree = ttk.Treeview(parent, columns=all_columns, displaycolumns=display_columns, show="headings")
        for col, config in self._get_column_info().items():
            self.result_tree.heading(col, text=config["text"])
            self.result_tree.column(col, width=config["width"], minwidth=config["width"]-40, anchor=tk.W)
        v_scrollbar = ttk.Scrollbar(parent, orient="vertical", command=self.result_tree.yview)
        h_scrollbar = ttk.Scrollbar(parent, orient="horizontal", command=self.result_tree.xview)
        self.result_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        self.result_tree.grid(row=2, column=0, sticky="nsew")
        v_scrollbar.grid(row=2, column=1, sticky="ns")
        h_scrollbar.grid(row=3, column=0, columnspan=2, sticky="ew")
        parent.grid_rowconfigure(2, weight=1)
        parent.grid_columnconfigure(0, weight=1)
        self._configure_treeview_tags()
        self.result_tree.bind("<<Paste>>", self._handle_paste_event)
        self.result_tree.bind("<ButtonRelease-1>", self._on_header_release)
    
    def _on_header_release(self, event):
        if self.result_tree.identify_region(event.x, event.y) == "separator":
            self.after(50, self._sync_filter_widths)

    def _sync_filter_widths(self):
        for i, col_id in enumerate(self._get_column_info().keys()):
            width = self.result_tree.column(col_id, "width")
            self.filter_frame.grid_columnconfigure(i, minsize=width)

    def on_filter_change_debounced(self, *args):
        if self._after_id: self.after_cancel(self._after_id)
        self._after_id = self.after(400, self.controller.refresh_view)

    def _create_status_bar(self):
        status_frame = tkb.Frame(self)
        status_frame.pack(side=BOTTOM, fill=X, padx=10, pady=(0,5))
        self.status_bar = tkb.Label(status_frame, text="", anchor=E, padding=(0, 2, 5, 2))
        self.status_bar.pack(side=RIGHT, fill=X, expand=True)
        self.progress_label = tkb.Label(status_frame, text="", width=35, anchor=W)
        self.progress_label.pack(side=LEFT, padx=5, pady=2)

    def _create_sql_options_dropdown(self, parent):
        self.sql_options_btn = tkb.Menubutton(parent, text="Tùy chọn SQL", bootstyle="outline")
        self.sql_options_btn.pack(side=LEFT, padx=5)
        menu = tk.Menu(self.sql_options_btn, tearoff=0)
        self.sql_options_btn["menu"] = menu
        menu.add_checkbutton(label="Cập nhật Tên KH", variable=self.update_ten_var)
        menu.add_checkbutton(label="Cập nhật Địa chỉ", variable=self.update_diachi_var)

    def _configure_treeview_tags(self):
        colors = self.style.colors
        tag_configs = {'active': {'foreground': colors.success}, 'inactive': {'foreground': colors.danger},
                       'warning': {'foreground': colors.warning}, 'error': {'foreground': colors.danger},
                       'addr_invalid': {'foreground': colors.danger}, 'pending': {'foreground': colors.secondary}}
        for tag, config in tag_configs.items(): self.result_tree.tag_configure(tag, **config)

    def _handle_paste_event(self, event=None):
        try:
            self.controller.handle_paste(self.clipboard_get())
        except tk.TclError: pass
    
    def update_treeview(self, data):
        selected_item = self.result_tree.focus()
        
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        filters = {col: var.get().lower() for col, var in self.filter_vars.items() if var.get()}
        total_rows, visible_rows = 0, 0
        
        for row in data:
            row_dict = dict(row)
            total_rows += 1

            match = all(filter_text in str(row_dict.get(col, '')).lower() for col, filter_text in filters.items())
            if not match: continue
            
            tags = self._get_row_tags(row_dict)
            
            _, tinh_display, xa_display = self.controller._validate_address(row_dict.get('dctstinh'), row_dict.get('dctstxa'))
            
            all_cols = list(self.result_tree['columns'])
            display_values = [row_dict.get(col, '') for col in all_cols]
            display_values[all_cols.index('dctstinh')] = tinh_display
            display_values[all_cols.index('dctstxa')] = xa_display
            
            item_id = str(row_dict.get('mst', ''))
            if item_id:
                self.result_tree.insert("", tk.END, values=tuple(display_values), tags=tuple(tags), iid=item_id)
                visible_rows += 1 # SỬA LỖI: Chỉ đếm các dòng được chèn vào
        
        if self.result_tree.exists(selected_item):
            self.result_tree.focus(selected_item)
            self.result_tree.selection_set(selected_item)
        self._update_status_bar_text(visible_rows, total_rows)

    def update_single_row(self, mst, data):
        if not self.result_tree.exists(mst): return
        
        tags = self._get_row_tags(data)
        _, tinh_display, xa_display = self.controller._validate_address(data.get('dctstinh'), data.get('dctstxa'))
        
        all_cols = list(self.result_tree['columns'])
        display_values = [data.get(col, '') for col in all_cols]
        display_values[all_cols.index('dctstinh')] = tinh_display
        display_values[all_cols.index('dctstxa')] = xa_display
        
        self.result_tree.item(mst, values=tuple(display_values), tags=tuple(tags))

    def _get_row_tags(self, row_data):
        tags = []
        if tthai := row_data.get('tthai'):
            if tthai.startswith(("00", "04")): tags.append('active')
            elif tthai.startswith(("01", "03", "06", "07")): tags.append('inactive')
            else: tags.append('warning')
        
        if ten := row_data.get('ten'):
            if "Không tìm thấy" in ten or "Lỗi" in ten: tags.append('error')
            if ten == 'Đang chờ...': tags.append('pending')

        if row_data.get('diachi_hop_le') != ADDR_VALID: 
            tags.append('addr_invalid')
        return tags

    def _update_status_bar_text(self, visible_count, total_count):
        dm_status = self.controller.dm_status
        self.original_status_text = f"Dòng: {visible_count} {dm_status}"
        self.status_bar.config(text=self.original_status_text)

    def get_visible_data(self):
        return [dict(zip(self.result_tree['columns'], self.result_tree.item(item_id, 'values'))) for item_id in self.result_tree.get_children()]

    def set_ui_state(self, enabled: bool):
        state = NORMAL if enabled else DISABLED
        widgets = [self.lookup_btn, self.relookup_btn, self.import_btn, self.delete_btn, self.export_btn, self.gen_sql_btn, self.sql_options_btn, self.thread_spinbox]
        for widget in widgets:
            if widget: widget.config(state=state)
        
        if not enabled:
            self.winfo_toplevel().bind('<Escape>', self.controller.cancel_lookup)
            self.progress_label.config(text="Đang chuẩn bị... (Esc để dừng)")
        else:
            self.winfo_toplevel().unbind('<Escape>')
            self.progress_label.config(text="")

    def update_progress(self, text=""):
        self.progress_label.config(text=f"Đang cập nhật: {text} (Esc để dừng)" if text else "")

    def show_sql_output(self, sql_string):
        output_window = tkb.Toplevel(self)
        output_window.title("Kết quả SQL")
        output_window.transient(self)
        output_window.grab_set()
        top_frame = tkb.Frame(output_window, padding=(10, 10, 10, 5))
        top_frame.pack(fill=X, side=TOP)
        def copy_to_clipboard_and_close():
            output_window.clipboard_clear()
            output_window.clipboard_append(sql_string)
            output_window.destroy()
            messagebox.showinfo("Thành công", "Đã sao chép vào clipboard!", parent=self)
        btn = tkb.Button(top_frame, text="Sao chép tất cả và Đóng", command=copy_to_clipboard_and_close, bootstyle="success")
        btn.pack(side=RIGHT)
        text_frame = tkb.Frame(output_window, padding=(10, 0, 10, 10))
        text_frame.pack(fill=BOTH, expand=True)
        sql_text = tk.Text(text_frame, wrap="word", font=("Segoe UI", 9))
        v_scroll = ttk.Scrollbar(text_frame, orient=VERTICAL, command=sql_text.yview)
        sql_text.configure(yscrollcommand=v_scroll.set)
        sql_text.pack(side=LEFT, fill=BOTH, expand=True)
        v_scroll.pack(side=RIGHT, fill=Y)
        sql_text.insert(tk.END, sql_string)
        sql_text.config(state=DISABLED)
        output_window.update_idletasks()
        parent = self.winfo_toplevel()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (950 // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (600 // 2)
        output_window.geometry(f"950x600+{x}+{y}")
        
    def show_export_success_dialog(self, filepath):
        dialog = tkb.Toplevel(self)
        dialog.title("Xuất thành công")
        dialog.transient(self)
        dialog.grab_set()
        parent = self.winfo_toplevel()
        dialog_w, dialog_h = 450, 150
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (dialog_w // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (dialog_h // 2)
        dialog.geometry(f"{dialog_w}x{dialog_h}+{x}+{y}")
        dialog.resizable(False, False)
        main_frame = tkb.Frame(dialog, padding=15)
        main_frame.pack(fill=BOTH, expand=True)
        msg_label = tkb.Label(main_frame, text="Dữ liệu đã được xuất thành công!", anchor=CENTER, font=("-size 10 -weight bold"))
        msg_label.pack(pady=(0, 5))
        path_label = tkb.Label(main_frame, text=filepath, wraplength=420, font=("-size 9 -slant italic"))
        path_label.pack(pady=(0, 15))
        btn_frame = tkb.Frame(main_frame)
        btn_frame.pack()
        def open_file(): self.controller.open_path(filepath); dialog.destroy()
        def open_folder(): self.controller.open_path(os.path.dirname(filepath)); dialog.destroy()
        tkb.Button(btn_frame, text="Mở File", command=open_file, bootstyle="success").pack(side=LEFT, padx=10)
        tkb.Button(btn_frame, text="Mở Thư mục", command=open_folder, bootstyle="primary-outline").pack(side=LEFT, padx=10)
        tkb.Button(btn_frame, text="Đóng", command=dialog.destroy, bootstyle="secondary").pack(side=LEFT, padx=10)

# ===================================================================================
# CONTROLLER
# ===================================================================================
class TaxLookupController:
    def __init__(self, model, admin_units_data):
        self.model = model
        self.view = None
        self.admin_units = admin_units_data
        self.dm_status = self._get_dm_status()
        self.cancel_event = threading.Event()
        self.thread_local = threading.local()

    def update_admin_units(self, admin_units_data):
        self.admin_units = admin_units_data
        self.dm_status = self._get_dm_status()
        self.refresh_view()

    def _get_dm_status(self):
        if not self.admin_units: return "| Lỗi: Không tải được danh mục"
        total_provinces = len(self.admin_units)
        total_wards = sum(len(v) for v in self.admin_units.values())
        return f"| Danh mục: {total_provinces} Tỉnh/TP, {total_wards} Phường/Xã"

    def _get_session(self):
        if not hasattr(self.thread_local, "session"):
            self.thread_local.session = requests.Session()
        return self.thread_local.session

    def lookup_tax_info_api(self, mst_number):
        session = self._get_session()
        url = f"https://hoadondientu.gdt.gov.vn:30000/category/public/dsdkts/{mst_number}/manager"
        headers = {'Accept': 'application/json, text/plain, */*', 'User-Agent': 'Mozilla/5.0'}
        try:
            response = session.get(url, headers=headers, timeout=10, verify=False)
            response.raise_for_status()
            if not response.text.strip(): return {"mst": mst_number, "error": "Phản hồi rỗng"}
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"mst": mst_number, "error": str(e)}
        except Exception as e:
            return {"mst": mst_number, "error": f"Lỗi không xác định: {str(e)}"}

    def initial_load(self):
        self.refresh_view()

    def _start_lookup_thread(self, msts_to_lookup):
        if not msts_to_lookup:
            messagebox.showinfo("Thông báo", "Không có Mã số thuế nào cần tra cứu.")
            return
        self.cancel_event.clear()
        self.view.set_ui_state(False)
        threading.Thread(target=self._run_lookup_task, args=(msts_to_lookup,), daemon=True).start()

    def start_lookup_pending(self):
        self._start_lookup_thread(self.model.get_records_to_lookup())

    def start_relookup_old(self):
        days = simpledialog.askinteger("Nhập số ngày", "Tra cứu lại các MST được cập nhật lần cuối hơn:", parent=self.view, minvalue=1, maxvalue=3650)
        if days: self._start_lookup_thread(self.model.get_outdated_records(days))

    def _run_lookup_task(self, msts_to_lookup):
        total_tasks = len(msts_to_lookup)
        completed_count = 0
        results_for_db = []
        
        try:
            try:
                num_workers = self.view.num_threads_var.get()
            except tk.TclError:
                num_workers = 10

            with ThreadPoolExecutor(max_workers=num_workers) as executor:
                future_to_mst = {executor.submit(self.lookup_tax_info_api, mst): mst for mst in msts_to_lookup}
                
                for future in as_completed(future_to_mst):
                    if self.cancel_event.is_set():
                        # Hủy các tác vụ chưa bắt đầu
                        for f in future_to_mst:
                            f.cancel()
                        break
                    
                    mst = future_to_mst[future]
                    try:
                        if future.cancelled():
                            continue
                        processed_data = self._process_api_result(mst, future.result())
                        results_for_db.append(processed_data)
                        self.view.after(0, self.view.update_single_row, mst, processed_data)
                    except Exception as e:
                        if not isinstance(e, CancelledError):
                            error_data = self._create_error_result(mst, str(e))
                            results_for_db.append(error_data)
                            self.view.after(0, self.view.update_single_row, mst, error_data)
                    
                    completed_count += 1
                    self.view.after(0, self.view.update_progress, f"{completed_count}/{total_tasks}")
            
            if results_for_db:
                print(f"Đang lưu {len(results_for_db)} kết quả đã tra cứu vào CSDL...")
                self.model.batch_update_records(results_for_db)
        
        finally:
            # Đảm bảo rằng hàm dọn dẹp luôn được gọi trên luồng chính
            self.view.after(0, self._lookup_finished)

    def _lookup_finished(self):
        if self.cancel_event.is_set():
            messagebox.showwarning("Đã dừng", "Quá trình tra cứu đã được người dùng dừng lại.\nCác kết quả đã tra cứu đã được lưu.")
        else:
            messagebox.showinfo("Hoàn tất", "Đã hoàn tất quá trình tra cứu và cập nhật CSDL!")
        self.view.set_ui_state(True)
        self.refresh_view()
        self.view.update_progress("")

    def cancel_lookup(self, event=None):
        if not self.cancel_event.is_set():
            self.cancel_event.set()

    def _create_base_result(self, mst):
        return {"mst": mst, "ten": None, "tthai": None, "dctsdchi": None, "dctstxa": None, "dctstinh": None, "diachi_daydu": None, "diachi_hop_le": None, "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

    def _create_error_result(self, mst, error_msg):
        error_result = self._create_base_result(mst)
        error_result.update({"ten": "Lỗi tra cứu", "tthai": error_msg})
        return error_result

    def _process_api_result(self, mst, api_result):
        final_data = self._create_base_result(mst)
        info_dict = api_result[0] if isinstance(api_result, list) and api_result else (api_result if isinstance(api_result, dict) and 'error' not in api_result else None)
        
        if not info_dict or not info_dict.get("tennnt"):
            update_data = {"ten": "❌ Không tìm thấy thông tin", "tthai": "Không hợp lệ", "diachi_hop_le": ADDR_NOT_FOUND}
        else:
            tthai_code = info_dict.get("tthai")
            dctstinh_raw, dctstxa_raw, dctsdchi = str(info_dict.get("dctstinhten", "")).strip(), str(info_dict.get("dctsxaten", "")).strip(), str(info_dict.get("dctsdchi", "")).strip()
            address_status, _, _ = self._validate_address(dctstinh_raw, dctstxa_raw)
            update_data = {
                "ten": info_dict.get("tennnt", ""), "tthai": f"{tthai_code} - {STATUS_MAP.get(tthai_code, 'Không xác định')}" if tthai_code else "",
                "dctsdchi": dctsdchi, "dctstxa": dctstxa_raw, "dctstinh": dctstinh_raw,
                "diachi_daydu": ", ".join(p for p in [dctsdchi, dctstxa_raw, dctstinh_raw] if p), "diachi_hop_le": address_status
            }
        final_data.update(update_data)
        return final_data

    def _validate_address(self, dctstinh_raw, dctstxa_raw):
        # Không có danh mục
        if not self.admin_units:
            return ADDR_NO_DIRECTORY, dctstinh_raw, dctstxa_raw

        # Thiếu một trong hai thành phần
        if not dctstinh_raw or not dctstxa_raw:
            return ADDR_INCOMPLETE, dctstinh_raw, dctstxa_raw

        # Chuẩn hóa tên tỉnh/TP người dùng nhập
        norm_tinh = normalize_str(dctstinh_raw)

        # Tìm khóa tỉnh/TP trong danh mục theo chuẩn hóa (keys trong danh mục đang là tên gốc có dấu)
        matched_province_key = None
        for prov_key in self.admin_units.keys():
            if normalize_str(prov_key) == norm_tinh:
                matched_province_key = prov_key
                break

        # Không khớp tên tỉnh/TP
        if not matched_province_key:
            return ADDR_INVALID_PROVINCE, f"❌ {dctstinh_raw}", dctstxa_raw

        # Lấy danh sách phường/xã và so khớp theo chuẩn hóa
        ward_list = self.admin_units.get(matched_province_key, []) or []
        norm_ward_set = {normalize_str(w) for w in ward_list}
        norm_xa = normalize_str(dctstxa_raw)

        if norm_xa not in norm_ward_set:
            return ADDR_INVALID_WARD, f"✅ {dctstinh_raw}", f"❌ {dctstxa_raw}"

        return ADDR_VALID, f"✅ {dctstinh_raw}", f"✅ {dctstxa_raw}"

    def refresh_view(self):
        if self.view: self.view.update_treeview(self.model.get_all_records())

    def handle_paste(self, clipboard_data):
        lines = [line.strip() for line in clipboard_data.splitlines() if line.strip()]
        
        # SỬA LỖI: Tự động bỏ qua dòng tiêu đề (header) khi dán dữ liệu.
        if lines and re.search(r'[^0-9-]', lines[0]):
            lines = lines[1:]

        sanitized_msts = [re.sub(r'[^a-zA-Z0-9-]', '', line) for line in lines if line]
        if sanitized_msts:
            added_count = self.model.add_msts(sanitized_msts)
            self.refresh_view()
            if added_count > 0: messagebox.showinfo("Thông báo", f"Đã thêm {added_count} mã số thuế mới.")
            else: messagebox.showinfo("Thông báo", "Các mã số thuế này đã tồn tại trong CSDL.")

    def delete_filtered_records(self):
        visible_data = self.view.get_visible_data()
        if not visible_data:
            messagebox.showwarning("Thông báo", "Không có mã số thuế nào đang được hiển thị để xóa.")
            return
        count = len(visible_data)
        if messagebox.askyesno("Xác nhận xóa", f"Bạn có chắc chắn muốn xóa {count} mã số thuế đang được lọc không?"):
            deleted_count = self.model.delete_records_by_mst([row['mst'] for row in visible_data])
            messagebox.showinfo("Hoàn tất", f"Đã xóa {deleted_count} mã số thuế.")
            self.refresh_view()

    def import_from_file(self):
        filepath = filedialog.askopenfilename(title="Chọn file MST", filetypes=[("Excel files", "*.xlsx"), ("Text/CSV files", "*.txt;*.csv"), ("All files", "*.*")])
        if not filepath: return
        msts_to_add = set()
        try:
            if filepath.endswith('.xlsx'):
                if not OPENPYXL_AVAILABLE:
                    messagebox.showerror("Thiếu thư viện", "Để đọc file Excel (.xlsx), bạn cần cài đặt thư viện 'openpyxl'.\n\nVui lòng chạy lệnh sau trong terminal:\npip install openpyxl")
                    return
                workbook = openpyxl.load_workbook(filepath, read_only=True)
                sheet = workbook.active
                
                all_rows_str = [str(row[0]) for row in sheet.iter_rows(min_row=1, max_col=1, values_only=True) if row and row[0]]

                # SỬA LỖI: Tự động bỏ qua dòng tiêu đề (header)
                if all_rows_str and re.search(r'[^0-9-]', all_rows_str[0]):
                    all_rows_str = all_rows_str[1:]

                for mst_str in all_rows_str:
                    msts_to_add.add(re.sub(r'[^a-zA-Z0-9-]', '', mst_str))
            else:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    all_lines = [line.strip() for line in f if line.strip()]

                    # SỬA LỖI: Tự động bỏ qua dòng tiêu đề (header)
                    if all_lines and re.search(r'[^0-9-]', all_lines[0].split(r'[,;\s\t]')[0]):
                        all_lines = all_lines[1:]

                    for line in all_lines:
                        for part in re.split(r'[,;\s\t]+', line):
                            if part:
                                msts_to_add.add(re.sub(r'[^a-zA-Z0-9-]', '', part))
            if msts_to_add:
                added_count = self.model.add_msts(list(msts_to_add))
                self.refresh_view()
                messagebox.showinfo("Thành công", f"Import hoàn tất. Đã thêm {added_count} mã số thuế mới.")
            else: messagebox.showwarning("Cảnh báo", "Không tìm thấy mã số thuế hợp lệ trong file.")
        except Exception as e: messagebox.showerror("Lỗi đọc file", f"Đã xảy ra lỗi khi xử lý file:\n{e}")

    def export_to_excel(self):
        if not OPENPYXL_AVAILABLE:
            messagebox.showerror("Thiếu thư viện", "Để xuất file Excel (.xlsx), bạn cần cài đặt thư viện 'openpyxl'.\n\nVui lòng chạy lệnh sau:\npip install openpyxl")
            return
        visible_data = self.view.get_visible_data()
        if not visible_data:
            messagebox.showwarning("Cảnh báo", "Không có dữ liệu để xuất.")
            return
        filepath = filedialog.asksaveasfilename(defaultextension=".xlsx", filetypes=[("Excel Workbook", "*.xlsx")], title="Lưu file Excel")
        if not filepath: return
        try:
            workbook = openpyxl.Workbook()
            sheet = workbook.active
            sheet.title = "Data Export"
            sheet.append(["Mã số thuế", "Tên NNT", "Trạng thái", "Số nhà/Đường", "Phường/Xã", "Tỉnh/TP", "Địa chỉ đầy đủ", "Trạng thái địa chỉ", "Cập nhật lần cuối"])
            for row in visible_data:
                sheet.append([str(row.get(k, "")).replace("✅ ", "").replace("❌ ", "") for k in ["mst", "ten", "tthai", "dctsdchi", "dctstxa", "dctstinh", "diachi_daydu", "diachi_hop_le", "last_updated"]])
            workbook.save(filepath)
            self.view.show_export_success_dialog(filepath)
        except Exception as e: messagebox.showerror("Lỗi", f"Không thể xuất file Excel: {e}")
    
    def open_path(self, path):
        try:
            if sys.platform == "win32": os.startfile(path)
            elif sys.platform == "darwin": subprocess.run(["open", path], check=True)
            else: subprocess.run(["xdg-open", path], check=True)
        except Exception as e: messagebox.showerror("Lỗi", f"Không thể mở đường dẫn:\n{path}\n\nLỗi: {e}")

    def generate_sql(self):
        visible_data = self.view.get_visible_data()
        if not visible_data:
            messagebox.showwarning("Cảnh báo", "Không có dữ liệu hợp lệ để tạo SQL.")
            return
        update_ten, update_diachi = self.view.update_ten_var.get(), self.view.update_diachi_var.get()
        if not update_ten and not update_diachi:
            messagebox.showerror("Lỗi", "Vui lòng chọn ít nhất một trường để cập nhật.")
            return
        sql_commands = []
        for row in visible_data:
            if not row.get('ten') or str(row.get('ten')).startswith(("❌", "Đang chờ")) or row.get('diachi_hop_le') != ADDR_VALID: continue
            ten_sql, diachi_sql, mst_sql = str(row['ten']).replace("'", "''"), str(row['diachi_daydu']).replace("'", "''"), str(row['mst']).strip().replace("'", "''")
            set_clauses = []
            if update_ten: set_clauses.append(f"ten_kh = N'{ten_sql}'")
            if update_diachi: set_clauses.append(f"dia_chi = N'{diachi_sql}'")
            if set_clauses: sql_commands.append(f"UPDATE dmkh SET {', '.join(set_clauses)} WHERE ma_so_thue = '{mst_sql}';")
        if not sql_commands:
            messagebox.showinfo("Thông báo", "Không có dữ liệu có địa chỉ hợp lệ để tạo SQL.")
            return
        self.view.show_sql_output("\n".join(sql_commands))

# ===================================================================================
# MAIN TAB CLASS
# ===================================================================================
class TaxLookupTab(ttk.Frame):
    def __init__(self, parent, admin_units_data):
        super().__init__(parent)
        self.model = TaxLookupModel()
        self.controller = TaxLookupController(self.model, admin_units_data)
        self.view = TaxLookupView(self, self.controller)
        self.controller.view = self.view
        self.controller.initial_load()
        self.winfo_toplevel().protocol("WM_DELETE_WINDOW", self._on_close)

    def _on_close(self):
        self.model.close_connection()
        self.winfo_toplevel().destroy()
