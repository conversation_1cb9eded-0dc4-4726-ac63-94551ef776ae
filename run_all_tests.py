#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script tổng hợp để chạy tất cả các test cho chức năng phân tích hóa đơn
"""

import sys
import os
import subprocess
import time

def run_test(test_file, test_name):
    """Chạy một test file và trả về kết quả"""
    print(f"\n{'='*60}")
    print(f"CHẠY TEST: {test_name}")
    print(f"File: {test_file}")
    print(f"{'='*60}")
    
    try:
        # Chạy test với UTF-8 encoding
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        result = subprocess.run([sys.executable, test_file],
                              capture_output=True,
                              text=True,
                              encoding='utf-8',
                              env=env,
                              cwd=os.path.dirname(os.path.abspath(__file__)))
        
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        success = result.returncode == 0
        
        if success:
            print(f"✅ {test_name} - THÀNH CÔNG")
        else:
            print(f"❌ {test_name} - THẤT BẠI (return code: {result.returncode})")
        
        return success
        
    except Exception as e:
        print(f"❌ Lỗi khi chạy {test_name}: {e}")
        return False

def main():
    """Main function"""
    print("🚀 BẮT ĐẦU CHẠY TẤT CẢ TESTS CHO CHỨC NĂNG PHÂN TÍCH HÓA ĐƠN")
    print(f"Thời gian: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Danh sách các test cần chạy
    tests = [
        ("test_invoice_analysis.py", "Unit Tests - Logic phân tích hóa đơn"),
        ("test_invoice_integration.py", "Integration Test - Dữ liệu thực tế"),
        ("test_invoice_error_detection.py", "Error Detection Test - Phát hiện lỗi"),
    ]
    
    results = []
    
    # Chạy từng test
    for test_file, test_name in tests:
        if os.path.exists(test_file):
            success = run_test(test_file, test_name)
            results.append((test_name, success))
        else:
            print(f"⚠️ File test không tồn tại: {test_file}")
            results.append((test_name, False))
    
    # Tổng kết
    print(f"\n{'='*60}")
    print("📊 TỔNG KẾT KẾT QUẢ TESTS")
    print(f"{'='*60}")
    
    success_count = 0
    total_count = len(results)
    
    for test_name, success in results:
        status = "✅ THÀNH CÔNG" if success else "❌ THẤT BẠI"
        print(f"{status:<15} - {test_name}")
        if success:
            success_count += 1
    
    print(f"\n📈 THỐNG KÊ:")
    print(f"   Tổng số tests: {total_count}")
    print(f"   Thành công: {success_count}")
    print(f"   Thất bại: {total_count - success_count}")
    print(f"   Tỷ lệ thành công: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print(f"\n🎉 TẤT CẢ TESTS ĐỀU THÀNH CÔNG!")
        print("✅ Chức năng phân tích hóa đơn hoạt động bình thường")
        return True
    else:
        print(f"\n⚠️ CÓ {total_count - success_count}/{total_count} TESTS THẤT BẠI!")
        print("❌ Cần kiểm tra và sửa lỗi")
        return False

def run_gui_test():
    """Chạy GUI test riêng biệt"""
    print(f"\n{'='*60}")
    print("🖥️ CHẠY GUI TEST")
    print(f"{'='*60}")
    
    gui_test_file = "test_gui_invoice_analysis.py"
    
    if not os.path.exists(gui_test_file):
        print(f"⚠️ File GUI test không tồn tại: {gui_test_file}")
        return False
    
    print("Lưu ý: GUI test sẽ mở cửa sổ ứng dụng và tự động test")
    print("Vui lòng không thao tác với chuột/bàn phím trong quá trình test")
    
    try:
        # Chạy GUI test với input tự động và UTF-8 encoding
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        process = subprocess.Popen([sys.executable, gui_test_file],
                                 stdin=subprocess.PIPE,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE,
                                 text=True,
                                 encoding='utf-8',
                                 env=env,
                                 cwd=os.path.dirname(os.path.abspath(__file__)))
        
        # Gửi input "1" để chọn auto test
        stdout, stderr = process.communicate(input="1\n", timeout=30)
        
        print(stdout)
        if stderr:
            print("STDERR:")
            print(stderr)
        
        success = process.returncode == 0
        
        if success:
            print("✅ GUI TEST - THÀNH CÔNG")
        else:
            print(f"❌ GUI TEST - THẤT BẠI (return code: {process.returncode})")
        
        return success
        
    except subprocess.TimeoutExpired:
        print("⏰ GUI test timeout - có thể do GUI không tự động đóng")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ Lỗi khi chạy GUI test: {e}")
        return False

def cleanup_test_files():
    """Dọn dẹp các file test không cần thiết"""
    print(f"\n{'='*60}")
    print("🧹 DỌN DẸP FILES TEST")
    print(f"{'='*60}")
    
    # Danh sách file cần giữ lại
    keep_files = [
        "run_all_tests.py",
        "test_invoice_analysis.py", 
        "test_invoice_integration.py",
        "test_invoice_error_detection.py",
        "test_gui_invoice_analysis.py"
    ]
    
    # Tìm các file test khác
    test_files = [f for f in os.listdir('.') if f.startswith('test_') and f.endswith('.py')]
    
    files_to_remove = [f for f in test_files if f not in keep_files]
    
    if files_to_remove:
        print("Các file test sẽ được xóa:")
        for f in files_to_remove:
            print(f"  - {f}")
        
        confirm = input("\nBạn có muốn xóa các file này không? (y/N): ").strip().lower()
        
        if confirm == 'y':
            for f in files_to_remove:
                try:
                    os.remove(f)
                    print(f"✅ Đã xóa: {f}")
                except Exception as e:
                    print(f"❌ Lỗi khi xóa {f}: {e}")
        else:
            print("Hủy bỏ việc dọn dẹp files")
    else:
        print("✅ Không có file test nào cần dọn dẹp")

if __name__ == "__main__":
    print("Chọn hành động:")
    print("1. Chạy tất cả unit/integration tests")
    print("2. Chạy GUI test")
    print("3. Chạy tất cả tests (bao gồm GUI)")
    print("4. Dọn dẹp test files")
    print("5. Thoát")
    
    choice = input("\nNhập lựa chọn (1-5): ").strip()
    
    if choice == "1":
        success = main()
        sys.exit(0 if success else 1)
    elif choice == "2":
        success = run_gui_test()
        sys.exit(0 if success else 1)
    elif choice == "3":
        print("Chạy unit/integration tests trước...")
        success1 = main()
        
        if success1:
            print("\nTiếp tục với GUI test...")
            success2 = run_gui_test()
            success = success1 and success2
        else:
            print("\nBỏ qua GUI test do unit tests thất bại")
            success = False
        
        sys.exit(0 if success else 1)
    elif choice == "4":
        cleanup_test_files()
        sys.exit(0)
    elif choice == "5":
        print("Thoát chương trình")
        sys.exit(0)
    else:
        print("Lựa chọn không hợp lệ")
        sys.exit(1)
