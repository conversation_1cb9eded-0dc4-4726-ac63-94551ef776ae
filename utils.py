import tkinter as tk
from tkinter import Toplevel
import ttkbootstrap as ttk

class Tooltip:
    """
    Tạo một c<PERSON>a sổ tooltip cho một widget.
    Hiển thị một đoạn text khi di chuột vào widget.
    """
    def __init__(self, widget, text='widget info'):
        self.widget = widget
        self.text = text
        self.tooltip_window = None
        self.id = None
        self.widget.bind("<Enter>", self.enter)
        self.widget.bind("<Leave>", self.leave)
        self.widget.bind("<ButtonPress>", self.leave)

    def enter(self, event=None):
        self.schedule()

    def leave(self, event=None):
        self.unschedule()
        self.hidetip()

    def schedule(self):
        self.unschedule()
        self.id = self.widget.after(500, self.showtip) # Delay 500ms

    def unschedule(self):
        if id := self.id:
            self.id = None
            self.widget.after_cancel(id)

    def showtip(self, event=None):
        if self.tooltip_window or not self.text:
            return
        
        x = self.widget.winfo_pointerx() + 15
        y = self.widget.winfo_pointery() + 10

        self.tooltip_window = Toplevel(self.widget)
        self.tooltip_window.wm_overrideredirect(True)
        self.tooltip_window.wm_geometry(f"+{x}+{y}")
        
        label = ttk.Label(
            self.tooltip_window, 
            text=self.text, 
            justify='left',
            wraplength=400, 
            bootstyle="inverse-dark",
            padding=8,
            borderwidth=1,
            relief="solid"
        )
        label.pack()
        label.bind("<Leave>", self.hidetip)
        self.tooltip_window.bind("<Leave>", self.hidetip)

    def hidetip(self, event=None):
        if tw := self.tooltip_window:
            self.tooltip_window = None
            try:
                tw.destroy()
            except tk.TclError:
                pass
            
    def set_text(self, text):
        self.text = text
