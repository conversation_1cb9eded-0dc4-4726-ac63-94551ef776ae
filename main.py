import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk
import os
import csv
import ctypes
import requests
import json
import io
import time
import threading
import unicodedata
import re

# --- IMPORT CÁC TAB ---
from sql_generator_tab import SqlGeneratorTab
from einvoice_debugger_tab import EInvoiceDebuggerTab
from tax_lookup_tab import TaxLookupTab

class App(ttk.Window):
    """
    Lớp ứng dụng chính (View/Controller chính), chịu trách nhiệm tạo cửa sổ,
    quản lý các tab và điều phối việc tải dữ liệu.
    """
    def __init__(self, themename="superhero"):
        super().__init__(themename=themename)
        self.title("Data Tools")
        self.geometry("1350x900")
        try:
            # Để sử dụng icon, bỏ comment dòng dưới và thay bằng đường dẫn file .ico
            # self.iconbitmap('icon.ico')
            pass
        except tk.TclError:
            print("<PERSON><PERSON><PERSON> ý: Không tìm thấy file icon. Bỏ qua.")

        # --- CẤU HÌNH GOOGLE SHEET ---
        self.SHEET_ID = '11TAYMWdEe5QPN5RoZEdmkWGhCTLFKnAyMf83E1z3y8o'
        self.DEFINITIONS_GID = '0'
        self.RULES_GID = '1897062167'
        self.ADMIN_UNITS_GID = '*********'
        # ----------------------------

        # --- KHUNG CHỨA CÁC NÚT HÀNH ĐỘNG TOÀN CỤC --
        action_frame = ttk.Frame(self, padding=(10, 10, 10, 0))
        action_frame.pack(fill='x', side='top')

        self.update_button = ttk.Button(
            action_frame,
            text="Cập nhật từ Google Sheet",
            command=self.refresh_all_data,
            bootstyle="success"
        )
        self.update_button.pack(side='left')

        self.status_label = ttk.Label(action_frame, text="Sẵn sàng.")
        self.status_label.pack(side='left', padx=10)

        # --- THÊM NÚT ĐỔI GIAO DIỆN ---
        self.create_theme_menu(action_frame)

        # --- KHUNG NOTEBOOK CHÍNH ĐỂ CHỨA CÁC TAB ---
        self.notebook = ttk.Notebook(self, padding=(10, 10))
        self.notebook.pack(expand=True, fill='both')

        # --- TẢI DỮ LIỆU BAN ĐẦU TỪ CACHE VÀ TẠO TABS ---
        self.create_tabs_from_cache()

    def create_theme_menu(self, parent_frame):
        """Tạo menu để thay đổi giao diện."""
        theme_menu_btn = ttk.Menubutton(parent_frame, text="Giao diện", bootstyle="outline")
        theme_menu_btn.pack(side='right', padx=5)
        menu = tk.Menu(theme_menu_btn, tearoff=0)
        theme_menu_btn["menu"] = menu
        for theme_name in sorted(self.style.theme_names()):
            menu.add_command(label=theme_name, command=lambda t=theme_name: self.change_theme(t))

    def change_theme(self, theme_name):
        """Thay đổi giao diện hiện tại của ứng dụng."""
        self.style.theme_use(theme_name)

    def create_tabs_from_cache(self):
        """
        Tải dữ liệu từ cache (nếu có) và tạo giao diện các tab.
        Hàm này không thực hiện yêu cầu mạng.
        """
        self.status_label.config(text="Đang tải dữ liệu từ cache...")
        self.field_definitions, self.field_mapping, self.doc_types = self._load_field_definitions(force_refresh=False)
        self.error_rules = self._load_error_rules(force_refresh=False)
        self.admin_units_data = self._load_admin_units_data(force_refresh=False)

        self._create_tabs_ui()
        self.status_label.config(text="Sẵn sàng. Dữ liệu được tải từ cache.")

    def load_and_create_tabs_from_network(self):
        """Tải tất cả dữ liệu từ Google Sheet và làm mới giao diện các tab."""
        try:
            # force_refresh=True để bắt buộc tải lại từ mạng
            self.field_definitions, self.field_mapping, self.doc_types = self._load_field_definitions(force_refresh=True)
            self.error_rules = self._load_error_rules(force_refresh=True)
            self.admin_units_data = self._load_admin_units_data(force_refresh=True)

            # Sử dụng self.after để đảm bảo các widget được cập nhật trong luồng chính
            self.after(0, self._update_tabs_data)
            self.after(0, lambda: self.status_label.config(text="Cập nhật thành công!"))

        except requests.exceptions.RequestException as e:
            self.after(0, lambda: self.status_label.config(text=f"Lỗi mạng: {e}", bootstyle="danger"))
            self.after(0, lambda: messagebox.showerror("Lỗi mạng", f"Không thể tải dữ liệu từ Google Sheet. Vui lòng kiểm tra kết nối mạng.\n{e}"))
        except Exception as e:
            self.after(0, lambda: self.status_label.config(text=f"Lỗi không xác định: {e}", bootstyle="danger"))
            self.after(0, lambda: messagebox.showerror("Lỗi không xác định", f"Đã xảy ra lỗi: {e}"))

    def _create_tabs_ui(self):
        """Tạo và thêm các tab vào notebook. Phải được gọi từ luồng chính."""
        # Tab 1: SQL Generator
        self.sql_tab = SqlGeneratorTab(self.notebook)
        self.notebook.add(self.sql_tab, text="Tạo Script SQL")

        # Tab 2: E-Invoice Debugger
        self.einvoice_tab = EInvoiceDebuggerTab(self.notebook, self.field_definitions, self.field_mapping, self.doc_types, self.error_rules)
        self.notebook.add(self.einvoice_tab, text="Gỡ lỗi Hóa đơn điện tử")

        # Tab 3: Tra cứu MST (deTax)
        self.tax_lookup_tab = TaxLookupTab(self.notebook, self.admin_units_data)
        self.notebook.add(self.tax_lookup_tab, text="Tra cứu MST (deTax)")

    def _update_tabs_data(self):
        """Cập nhật dữ liệu cho các tab đã tồn tại thay vì tạo lại chúng."""
        if hasattr(self, 'einvoice_tab'):
            self.einvoice_tab.update_all_data(self.field_definitions, self.field_mapping, self.doc_types, self.error_rules)

        if hasattr(self, 'tax_lookup_tab'):
             self.tax_lookup_tab.controller.update_admin_units(self.admin_units_data)


    def refresh_all_data(self):
        """
        Bắt đầu một luồng mới để tải lại toàn bộ dữ liệu từ Google Sheet.
        Đây là vai trò Controller, điều phối tác vụ nền.
        """
        self.status_label.config(text="Đang cập nhật từ Google Sheet...", bootstyle="info")
        self.update_button.config(state="disabled")

        def refresh_task():
            self.load_and_create_tabs_from_network()
            # Sau khi hoàn tất, bật lại nút trong luồng chính
            self.after(0, lambda: self.update_button.config(state="normal"))

        threading.Thread(target=refresh_task, daemon=True).start()

    # ===================================================================================
    # CÁC HÀM TẢI VÀ XỬ LÝ DỮ LIỆU (Vai trò Model)
    # ===================================================================================

    def _load_data_from_gsheet(self, url: str, cache_file: str, parser_func, force_refresh: bool = False):
        """
        Tải dữ liệu. Ưu tiên cache trừ khi force_refresh=True.
        """
        cache_path = os.path.join(os.path.dirname(__file__), 'cache')
        os.makedirs(cache_path, exist_ok=True)
        cache_file_path = os.path.join(cache_path, cache_file)

        if not force_refresh:
            if os.path.exists(cache_file_path):
                try:
                    with open(cache_file_path, 'r', encoding='utf-8') as f:
                        print(f"Đang tải dữ liệu từ cache: {cache_file}")
                        return json.load(f)
                except (json.JSONDecodeError, FileNotFoundError) as e:
                    print(f"Lỗi đọc cache {cache_file}: {e}. Sẽ tải từ mạng.")
            else:
                 print(f"Cache file {cache_file} không tồn tại. Sẽ tải từ mạng.")

        print(f"Đang tải dữ liệu từ Google Sheet: {url.split('gid=')[-1]}")
        self.after(0, lambda: self.status_label.config(text=f"Đang tải {cache_file}..."))

        response = requests.get(url, timeout=20)
        response.raise_for_status()

        csv_text = response.content.decode('utf-8')
        data = parser_func(csv_text)

        with open(cache_file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        return data

    def _parse_field_definitions(self, csv_text: str):
        """
        CẬP NHẬT: Phân tích sheet định nghĩa field bằng cách đọc tiêu đề cột.
        """
        f = io.StringIO(csv_text)
        reader = csv.reader(f)

        try:
            header_row = next(reader)
            header_map = {header.strip().lower(): i for i, header in enumerate(header_row)}
        except StopIteration:
            return {}, {}, []

        required_cols = ['doctype', 'level', 'name', 'json', 'xml', 'print', 'description']
        missing_cols = [col for col in required_cols if col not in header_map]
        if missing_cols:
            raise ValueError(f"Lỗi Sheet 'Definitions': Thiếu các cột bắt buộc: {', '.join(missing_cols)}")

        tooltip_data, field_mapping, doc_types = {}, {}, set()
        for row in reader:
            if len(row) < len(header_map): continue

            doc_type     = row[header_map['doctype']].strip()
            level        = row[header_map['level']].strip()
            content_name = row[header_map['name']].strip()
            json_tag     = row[header_map['json']].strip()
            xml_tag      = row[header_map['xml']].strip()
            print_name   = row[header_map['print']].strip()
            desc         = row[header_map['description']].strip()

            if not all([doc_type, level, content_name]): continue
            doc_type_lower = doc_type.lower()
            doc_types.add(doc_type)
            tooltip_data.setdefault(doc_type_lower, {}).setdefault(level, {})[content_name] = {
                "desc": desc, "print_name": print_name, "json_tag": json_tag, "xml_tag": xml_tag
            }
            if json_tag: field_mapping[json_tag] = content_name
            if xml_tag: field_mapping[xml_tag] = content_name

        return tooltip_data, field_mapping, sorted(list(doc_types))

    def _parse_error_rules(self, csv_text: str):
        """
        CẬP NHẬT: Phân tích sheet luật lỗi bằng cách đọc tiêu đề cột.
        """
        f = io.StringIO(csv_text)
        reader = csv.reader(f)

        try:
            header_row = next(reader)
            header_map = {header.strip().lower(): i for i, header in enumerate(header_row)}
        except StopIteration:
            return []

        required_cols = ['doctype', 'error', 'query']
        missing_cols = [col for col in required_cols if col not in header_map]
        if missing_cols:
             raise ValueError(f"Lỗi Sheet 'Rules': Thiếu các cột bắt buộc: {', '.join(missing_cols)}")

        rules = []
        for row in reader:
            if len(row) < len(header_map): continue

            rule_doc_type = row[header_map['doctype']].strip()
            rule_name = row[header_map['error']].strip()
            rule_condition = row[header_map['query']].strip()

            if rule_name and rule_condition:
                rules.append({"doc_type": rule_doc_type, "name": rule_name, "condition": rule_condition})
        return rules

    def _normalize_str(self, s: str):
        if not isinstance(s, str): return ""
        s_norm = ''.join(c for c in unicodedata.normalize('NFD', s.strip().lower()) if unicodedata.category(c) != 'Mn')
        prefixes = [r'^(thanh pho|tp|tinh|t|phuong|p|xa|x|quan|q|huyen|h)\.?\s+']
        for p in prefixes: s_norm = re.sub(p, '', s_norm, count=1)
        return re.sub(r'\s+', ' ', s_norm).strip()

    def _parse_admin_units(self, csv_text: str):
        """
        CẬP NHẬT: Giữ lại dấu cho cả Tỉnh/Thành phố và Phường/Xã.
        """
        f = io.StringIO(csv_text)
        reader = csv.reader(f)

        try:
            header_row = next(reader)
            header_map = {self._normalize_str(h): i for i, h in enumerate(header_row)}
        except StopIteration:
            return {}

        ward_key = 'phuong/xa'
        province_key = 'tinh/thanh pho'
        
        if ward_key not in header_map or province_key not in header_map:
            raise ValueError(f"Lỗi Sheet 'Admin Units': Thiếu cột 'Phường/Xã' hoặc 'Tỉnh/Thành phố'")

        temp_admin_units = {}
        province_name_map = {}

        for row in reader:
            if len(row) < len(header_map): continue
            
            original_ward = row[header_map[ward_key]].strip()
            original_province = row[header_map[province_key]].strip()

            if original_province and original_ward:
                norm_province = self._normalize_str(original_province)
                
                # Không cần chuẩn hóa tên phường/xã nữa nếu chỉ dùng để lưu trữ
                # norm_ward = self._normalize_str(original_ward) 
                
                if norm_province:
                    if norm_province not in province_name_map:
                        province_name_map[norm_province] = original_province
                    
                    # SỬA LỖI: Thêm tên phường/xã GỐC (có dấu) vào set
                    temp_admin_units.setdefault(norm_province, set()).add(original_ward)

        final_units = {}
        for norm_province, wards in temp_admin_units.items():
            original_province_name = province_name_map[norm_province]
            # Danh sách `wards` giờ đây đã chứa các tên có dấu
            final_units[original_province_name] = sorted(list(wards))
            
        return final_units

    def _load_field_definitions(self, force_refresh: bool = False) -> tuple:
        url = f'https://docs.google.com/spreadsheets/d/{self.SHEET_ID}/export?format=csv&gid={self.DEFINITIONS_GID}'
        data = self._load_data_from_gsheet(url, 'field_definitions.json', self._parse_field_definitions, force_refresh)
        return data if data is not None else ({}, {}, [])

    def _load_error_rules(self, force_refresh: bool = False) -> list:
        url = f'https://docs.google.com/spreadsheets/d/{self.SHEET_ID}/export?format=csv&gid={self.RULES_GID}'
        rules = self._load_data_from_gsheet(url, 'error_rules.json', self._parse_error_rules, force_refresh)
        return rules if rules is not None else []

    def _load_admin_units_data(self, force_refresh: bool = False) -> dict:
        url = f'https://docs.google.com/spreadsheets/d/{self.SHEET_ID}/export?format=csv&gid={self.ADMIN_UNITS_GID}'
        units = self._load_data_from_gsheet(url, 'admin_units.json', self._parse_admin_units, force_refresh)
        return units if units is not None else {}


if __name__ == "__main__":
    try:
        # Cải thiện hiển thị trên màn hình có DPI cao trên Windows
        ctypes.windll.shcore.SetProcessDpiAwareness(1)
    except (ImportError, AttributeError, OSError):
        # Bỏ qua nếu không phải Windows hoặc không có thư viện
        pass

    app = App(themename="superhero")
    app.mainloop()