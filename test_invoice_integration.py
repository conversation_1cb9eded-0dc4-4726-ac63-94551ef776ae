#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Integration test để kiểm tra chức năng phân tích hóa đơn với dữ liệu thực tế
"""

import json
import sys
import os

# Thêm thư mục gốc vào sys.path để import được các module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_invoice_analysis_integration():
    """Test integration với dữ liệu hóa đơn thực tế"""
    
    # Sample JSON data từ user
    sample_json = {
        "data": {
            "structure": {
                "master": ["Key","InvoiceDate","CustomerCode","Buyer","CustomerName","CustomerTaxCode","CustomerType","Address","PhoneNumber","FaxNumber","EmailDeliver","BankAccount","BankName","PaymentMethod","Currency","ExchangeRate","Amount","TotalAmount","TaxRate","TaxAmount","TaxAmount5","TaxAmount10","AmountInWords","HumanName","DiscountAmount","PromotionAmount","Note","VoucherType","IDCardNo","PassportNo","BuyerUnit"],
                "detail": ["ItemCode","ItemName","UOM","IsPromotion","Quantity","Price","Amount","TaxRate","TaxAmount","DiscountAmount","ProcessType"]
            },
            "invoices": [{
                "master": ["A000005943HDA","07/08/2025","**********","","CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ PHÚ HƯNG","**********",None,"Phòng Số 4, Tầng 21, Tòa Nhà Phú Mỹ Hưng, Số 8 Đường Hoàng Văn Thái, Khu Phố 1, Phường Tân Phú, Quận 7, Thành Phố Hồ Chí Minh, Việt Nam","**********",None,"<EMAIL>","","","TM/CK","VND",1.************,800000.00,864000.00,8.00,64000.00,None,None,"Tám trăm sáu mươi bốn nghìn đồng chẵn","FAST",0.00,0.00,"0ÿ",None,"","",""],
                "detail": [
                    [None,"Carrot Bread","Phần",0,20.000,20000.0000,400000.00,8.00,32000.00,0.00,"1"],
                    [None,"SERVICE FEE MAC LEVEL 1 (661-17548)","Cái",0,20.000,20000.0000,400000.00,8.00,32000.00,0.00,"1"]
                ]
            }]
        },
        "voucherBook": "BMRK25",
        "adjustmentType": 0
    }
    
    print("=== KIỂM TRA CHỨC NĂNG PHÂN TÍCH HÓA ĐƠN ===\n")
    
    # Test 1: Kiểm tra parsing JSON
    print("1. Kiểm tra parsing JSON...")
    try:
        from einvoice_debugger_tab import JSON_TO_SQL_MAPPING, DETAIL_SPECIFIC_MAPPING
        
        # Tạo parser helper
        class JsonParser:
            def _map_json_to_sql_fields(self, data_dict: dict, is_master: bool = True) -> dict:
                mapped_dict = {}
                for json_key, value in data_dict.items():
                    if not is_master and json_key in DETAIL_SPECIFIC_MAPPING:
                        sql_key = DETAIL_SPECIFIC_MAPPING[json_key]
                    else:
                        sql_key = JSON_TO_SQL_MAPPING.get(json_key, json_key)
                    mapped_dict[sql_key] = value
                return mapped_dict
            
            def _parse_json_input_data(self, raw_data: dict) -> dict:
                parsed = {"master": {}, "detail": [], "other": {}}
                if 'data' in raw_data and isinstance(raw_data['data'], dict):
                    invoice_data = raw_data['data']
                    structure = invoice_data.get('structure', {})
                    invoices = invoice_data.get('invoices', [])
                    master_header = structure.get('master', [])
                    detail_header = structure.get('detail', [])
                    if invoices:
                        master_values = invoices[0].get('master', [])
                        raw_master = dict(zip(master_header, master_values))
                        sql_mapped_master = self._map_json_to_sql_fields(raw_master, is_master=True)
                        parsed['master'] = sql_mapped_master
                        
                        detail_rows = invoices[0].get('detail', [])
                        parsed['detail'] = []
                        for row in detail_rows:
                            raw_detail_row = dict(zip(detail_header, row))
                            sql_mapped_detail = self._map_json_to_sql_fields(raw_detail_row, is_master=False)
                            parsed['detail'].append(sql_mapped_detail)
                            
                    for key, value in raw_data.items():
                        if key != 'data': parsed['other'][key] = value
                return parsed
        
        parser = JsonParser()
        parsed_data = parser._parse_json_input_data(sample_json)
        
        print("   ✓ Parsing JSON thành công")
        print(f"   ✓ Master fields: {len(parsed_data['master'])}")
        print(f"   ✓ Detail rows: {len(parsed_data['detail'])}")
        print(f"   ✓ Other fields: {len(parsed_data['other'])}")
        
        # Hiển thị một số field quan trọng
        master = parsed_data['master']
        print(f"   ✓ Tên đơn vị mua hàng: {master.get('Tên_đơn_vị_mua_hàng', 'N/A')}")
        print(f"   ✓ Tổng tiền hàng: {master.get('Tổng_tiền_hàng', 'N/A')}")
        print(f"   ✓ Tổng tiền thanh toán: {master.get('Tổng_tiền_thanh_toán', 'N/A')}")
        
        if parsed_data['detail']:
            detail_item = parsed_data['detail'][0]
            print(f"   ✓ Tên vật tư đầu tiên: {detail_item.get('Tên_vật_tư', 'N/A')}")
            print(f"   ✓ Số lượng: {detail_item.get('Số_lượng', 'N/A')}")
            print(f"   ✓ Tiền hàng: {detail_item.get('Tiền_hàng', 'N/A')}")
        
    except Exception as e:
        print(f"   ✗ Lỗi parsing JSON: {e}")
        return False
    
    # Test 2: Kiểm tra validation
    print("\n2. Kiểm tra validation...")
    try:
        from einvoice_debugger_tab import EInvoiceDebuggerModel
        
        # Tạo model với rules
        sample_rules = [
            {
                "doc_type": "Hóa đơn bán hàng",
                "name": "[Thông tin chung] Tên đơn vị mua hàng không được để trống.",
                "condition": "SELECT 1 FROM master WHERE Tên_đơn_vị_mua_hàng IS NULL OR Tên_đơn_vị_mua_hàng = ''"
            },
            {
                "doc_type": "Hóa đơn bán hàng", 
                "name": "[Chi tiết] Có dòng hàng với số lượng nhỏ hơn hoặc bằng 0.",
                "condition": "SELECT 1 FROM details WHERE Số_lượng <= 0"
            },
            {
                "doc_type": "Hóa đơn bán hàng",
                "name": "[Đối chiếu] Tổng tiền hàng chi tiết không khớp với tổng tiền hàng chung.",
                "condition": "SELECT 1 FROM master WHERE ABS(Tổng_tiền_hàng - (SELECT SUM(Tiền_hàng) FROM details)) > 1"
            }
        ]
        
        model = EInvoiceDebuggerModel(sample_rules)
        
        validation_data = {
            "master": parsed_data.get("master", {}),
            "detail": parsed_data.get("detail", [])
        }
        
        errors = model.validate_data("Hóa đơn bán hàng", validation_data)
        
        print(f"   ✓ Validation hoàn thành")
        print(f"   ✓ Số lỗi tìm thấy: {len(errors)}")
        
        if errors:
            print("   Danh sách lỗi:")
            for i, error in enumerate(errors, 1):
                print(f"     {i}. {error}")
        else:
            print("   ✓ Không có lỗi nào được tìm thấy")
        
    except Exception as e:
        print(f"   ✗ Lỗi validation: {e}")
        return False
    
    # Test 3: Kiểm tra tính toán
    print("\n3. Kiểm tra tính toán...")
    try:
        master = parsed_data['master']
        detail = parsed_data['detail']
        
        # Kiểm tra tổng tiền hàng
        total_amount_master = float(master.get('Tổng_tiền_hàng', 0))
        total_amount_detail = sum(float(item.get('Tiền_hàng', 0)) for item in detail)
        
        print(f"   ✓ Tổng tiền hàng (master): {total_amount_master:,.2f}")
        print(f"   ✓ Tổng tiền hàng (detail): {total_amount_detail:,.2f}")
        print(f"   ✓ Chênh lệch: {abs(total_amount_master - total_amount_detail):,.2f}")
        
        if abs(total_amount_master - total_amount_detail) <= 1:
            print("   ✓ Tổng tiền hàng khớp nhau")
        else:
            print("   ⚠ Tổng tiền hàng không khớp")
        
        # Kiểm tra tổng tiền thuế
        total_tax_master = float(master.get('Tổng_tiền_thuế', 0))
        total_tax_detail = sum(float(item.get('Tiền_thuế', 0)) for item in detail)
        
        print(f"   ✓ Tổng tiền thuế (master): {total_tax_master:,.2f}")
        print(f"   ✓ Tổng tiền thuế (detail): {total_tax_detail:,.2f}")
        print(f"   ✓ Chênh lệch: {abs(total_tax_master - total_tax_detail):,.2f}")
        
        if abs(total_tax_master - total_tax_detail) <= 1:
            print("   ✓ Tổng tiền thuế khớp nhau")
        else:
            print("   ⚠ Tổng tiền thuế không khớp")
        
    except Exception as e:
        print(f"   ✗ Lỗi kiểm tra tính toán: {e}")
        return False
    
    print("\n=== KẾT QUẢ TỔNG QUAN ===")
    print("✓ Chức năng phân tích hóa đơn hoạt động bình thường")
    print("✓ Parsing JSON thành công")
    print("✓ Validation rules hoạt động đúng")
    print("✓ Tính toán và đối chiếu chính xác")
    
    return True

if __name__ == "__main__":
    success = test_invoice_analysis_integration()
    if success:
        print("\n🎉 TẤT CẢ TESTS ĐỀU THÀNH CÔNG!")
    else:
        print("\n❌ CÓ LỖI XẢY RA TRONG QUÁ TRÌNH TEST!")
    
    sys.exit(0 if success else 1)
