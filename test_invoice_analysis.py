#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để kiểm tra chức năng phân tích hóa đơn
"""

import json
import sys
import os
import unittest
from unittest.mock import Mock, patch

# Th<PERSON><PERSON> thư mục gốc vào sys.path để import đượ<PERSON> các module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from einvoice_debugger_tab import EInvoiceDebuggerModel, JSON_TO_SQL_MAPPING, DETAIL_SPECIFIC_MAPPING

class TestInvoiceAnalysis(unittest.TestCase):
    """Test class cho chức năng phân tích hóa đơn"""
    
    def setUp(self):
        """Thiết lập dữ liệu test"""
        self.model = EInvoiceDebuggerModel()
        
        # Sample JSON data từ user
        self.sample_json = {
            "data": {
                "structure": {
                    "master": ["Key","InvoiceDate","CustomerCode","Buyer","CustomerName","CustomerTaxCode","CustomerType","Address","PhoneNumber","FaxNumber","EmailDeliver","BankAccount","BankName","PaymentMethod","Currency","ExchangeRate","Amount","TotalAmount","TaxRate","TaxAmount","TaxAmount5","TaxAmount10","AmountInWords","HumanName","DiscountAmount","PromotionAmount","Note","VoucherType","IDCardNo","PassportNo","BuyerUnit"],
                    "detail": ["ItemCode","ItemName","UOM","IsPromotion","Quantity","Price","Amount","TaxRate","TaxAmount","DiscountAmount","ProcessType"]
                },
                "invoices": [{
                    "master": ["A000005943HDA","07/08/2025","**********","","CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ PHÚ HƯNG","**********",None,"Phòng Số 4, Tầng 21, Tòa Nhà Phú Mỹ Hưng, Số 8 Đường Hoàng Văn Thái, Khu Phố 1, Phường Tân Phú, Quận 7, Thành Phố Hồ Chí Minh, Việt Nam","**********",None,"<EMAIL>","","","TM/CK","VND",1.************,800000.00,864000.00,8.00,64000.00,None,None,"Tám trăm sáu mươi bốn nghìn đồng chẵn","FAST",0.00,0.00,"0ÿ",None,"","",""],
                    "detail": [
                        [None,"Carrot Bread","Phần",0,20.000,20000.0000,400000.00,8.00,32000.00,0.00,"1"],
                        [None,"SERVICE FEE MAC LEVEL 1 (661-17548)","Cái",0,20.000,20000.0000,400000.00,8.00,32000.00,0.00,"1"]
                    ]
                }]
            },
            "voucherBook": "BMRK25",
            "adjustmentType": 0
        }
        
        # Sample rules để test validation
        self.sample_rules = [
            {
                "doc_type": "Hóa đơn bán hàng",
                "name": "[Thông tin chung] Tên đơn vị mua hàng không được để trống.",
                "condition": "SELECT 1 FROM master WHERE Tên_đơn_vị_mua_hàng IS NULL OR Tên_đơn_vị_mua_hàng = ''"
            },
            {
                "doc_type": "Hóa đơn bán hàng", 
                "name": "[Chi tiết] Có dòng hàng với số lượng nhỏ hơn hoặc bằng 0.",
                "condition": "SELECT 1 FROM details WHERE Số_lượng <= 0"
            },
            {
                "doc_type": "Hóa đơn bán hàng",
                "name": "[Đối chiếu] Tổng tiền hàng chi tiết không khớp với tổng tiền hàng chung.",
                "condition": "SELECT 1 FROM master WHERE ABS(Tổng_tiền_hàng - (SELECT SUM(Tiền_hàng) FROM details)) > 1"
            }
        ]
        
        self.model.set_rules(self.sample_rules)

    def test_json_to_sql_mapping(self):
        """Test mapping từ JSON field names sang Vietnamese SQL field names"""
        # Test master field mapping
        self.assertEqual(JSON_TO_SQL_MAPPING.get("CustomerName"), "Tên_đơn_vị_mua_hàng")
        self.assertEqual(JSON_TO_SQL_MAPPING.get("InvoiceDate"), "Ngày_hóa_đơn")
        self.assertEqual(JSON_TO_SQL_MAPPING.get("Amount"), "Tổng_tiền_hàng")
        
        # Test detail specific mapping
        self.assertEqual(DETAIL_SPECIFIC_MAPPING.get("Amount"), "Tiền_hàng")
        self.assertEqual(DETAIL_SPECIFIC_MAPPING.get("TaxAmount"), "Tiền_thuế")

    def test_parse_json_input_data(self):
        """Test parsing JSON input data"""
        # Tạo một class helper để test parsing logic mà không cần GUI
        class JsonParser:
            def _map_json_to_sql_fields(self, data_dict: dict, is_master: bool = True) -> dict:
                """Map JSON field names to Vietnamese SQL field names"""
                mapped_dict = {}
                for json_key, value in data_dict.items():
                    if not is_master and json_key in DETAIL_SPECIFIC_MAPPING:
                        sql_key = DETAIL_SPECIFIC_MAPPING[json_key]
                    else:
                        sql_key = JSON_TO_SQL_MAPPING.get(json_key, json_key)
                    mapped_dict[sql_key] = value
                return mapped_dict

            def _parse_json_input_data(self, raw_data: dict) -> dict:
                parsed = {"master": {}, "detail": [], "other": {}}
                if 'data' in raw_data and isinstance(raw_data['data'], dict):
                    invoice_data = raw_data['data']
                    structure = invoice_data.get('structure', {})
                    invoices = invoice_data.get('invoices', [])
                    master_header = structure.get('master', [])
                    detail_header = structure.get('detail', [])
                    if invoices:
                        master_values = invoices[0].get('master', [])
                        raw_master = dict(zip(master_header, master_values))
                        sql_mapped_master = self._map_json_to_sql_fields(raw_master, is_master=True)
                        parsed['master'] = sql_mapped_master

                        detail_rows = invoices[0].get('detail', [])
                        parsed['detail'] = []
                        for row in detail_rows:
                            raw_detail_row = dict(zip(detail_header, row))
                            sql_mapped_detail = self._map_json_to_sql_fields(raw_detail_row, is_master=False)
                            parsed['detail'].append(sql_mapped_detail)

                    for key, value in raw_data.items():
                        if key != 'data': parsed['other'][key] = value
                return parsed

        parser = JsonParser()
        parsed_data = parser._parse_json_input_data(self.sample_json)

        # Kiểm tra cấu trúc parsed data
        self.assertIn("master", parsed_data)
        self.assertIn("detail", parsed_data)
        self.assertIn("other", parsed_data)

        # Kiểm tra master data đã được map đúng
        master_data = parsed_data["master"]
        self.assertIn("Tên_đơn_vị_mua_hàng", master_data)
        self.assertEqual(master_data["Tên_đơn_vị_mua_hàng"], "CÔNG TY CỔ PHẦN QUẢN LÝ QUỸ PHÚ HƯNG")

        # Kiểm tra detail data
        detail_data = parsed_data["detail"]
        self.assertEqual(len(detail_data), 2)

        # Kiểm tra detail item đầu tiên
        first_detail = detail_data[0]
        self.assertIn("Tên_vật_tư", first_detail)
        self.assertEqual(first_detail["Tên_vật_tư"], "Carrot Bread")
        self.assertIn("Tiền_hàng", first_detail)  # Mapped từ Amount trong detail context
        self.assertEqual(first_detail["Tiền_hàng"], 400000.00)

    def test_validate_data_with_sample_json(self):
        """Test validation với sample JSON data"""
        # Sử dụng JsonParser helper class
        class JsonParser:
            def _map_json_to_sql_fields(self, data_dict: dict, is_master: bool = True) -> dict:
                mapped_dict = {}
                for json_key, value in data_dict.items():
                    if not is_master and json_key in DETAIL_SPECIFIC_MAPPING:
                        sql_key = DETAIL_SPECIFIC_MAPPING[json_key]
                    else:
                        sql_key = JSON_TO_SQL_MAPPING.get(json_key, json_key)
                    mapped_dict[sql_key] = value
                return mapped_dict

            def _parse_json_input_data(self, raw_data: dict) -> dict:
                parsed = {"master": {}, "detail": [], "other": {}}
                if 'data' in raw_data and isinstance(raw_data['data'], dict):
                    invoice_data = raw_data['data']
                    structure = invoice_data.get('structure', {})
                    invoices = invoice_data.get('invoices', [])
                    master_header = structure.get('master', [])
                    detail_header = structure.get('detail', [])
                    if invoices:
                        master_values = invoices[0].get('master', [])
                        raw_master = dict(zip(master_header, master_values))
                        sql_mapped_master = self._map_json_to_sql_fields(raw_master, is_master=True)
                        parsed['master'] = sql_mapped_master

                        detail_rows = invoices[0].get('detail', [])
                        parsed['detail'] = []
                        for row in detail_rows:
                            raw_detail_row = dict(zip(detail_header, row))
                            sql_mapped_detail = self._map_json_to_sql_fields(raw_detail_row, is_master=False)
                            parsed['detail'].append(sql_mapped_detail)

                    for key, value in raw_data.items():
                        if key != 'data': parsed['other'][key] = value
                return parsed

        parser = JsonParser()
        parsed_data = parser._parse_json_input_data(self.sample_json)

        # Validate data
        validation_data = {
            "master": parsed_data.get("master", {}),
            "detail": parsed_data.get("detail", [])
        }

        errors = self.model.validate_data("Hóa đơn bán hàng", validation_data)

        # In ra errors để debug
        print("Validation errors:")
        for error in errors:
            print(f"  - {error}")

        # Kiểm tra không có lỗi về tên đơn vị mua hàng (vì có dữ liệu)
        name_errors = [e for e in errors if "Tên đơn vị mua hàng" in e]
        self.assertEqual(len(name_errors), 0, "Không nên có lỗi về tên đơn vị mua hàng")

        # Kiểm tra không có lỗi về số lượng (vì số lượng > 0)
        quantity_errors = [e for e in errors if "số lượng" in e.lower()]
        self.assertEqual(len(quantity_errors), 0, "Không nên có lỗi về số lượng")

    def test_field_mapping_consistency(self):
        """Test tính nhất quán của field mapping"""
        # Kiểm tra các field quan trọng có mapping
        important_fields = ["CustomerName", "InvoiceDate", "Amount", "TotalAmount", "ItemName", "Quantity", "Price"]
        
        for field in important_fields:
            if field in ["Amount", "TaxAmount", "DiscountAmount"]:
                # Các field này có mapping khác nhau tùy context
                continue
            self.assertIn(field, JSON_TO_SQL_MAPPING, f"Field {field} should have mapping")

    def test_data_types_conversion(self):
        """Test chuyển đổi kiểu dữ liệu"""
        # Sử dụng JsonParser helper class
        class JsonParser:
            def _map_json_to_sql_fields(self, data_dict: dict, is_master: bool = True) -> dict:
                mapped_dict = {}
                for json_key, value in data_dict.items():
                    if not is_master and json_key in DETAIL_SPECIFIC_MAPPING:
                        sql_key = DETAIL_SPECIFIC_MAPPING[json_key]
                    else:
                        sql_key = JSON_TO_SQL_MAPPING.get(json_key, json_key)
                    mapped_dict[sql_key] = value
                return mapped_dict

            def _parse_json_input_data(self, raw_data: dict) -> dict:
                parsed = {"master": {}, "detail": [], "other": {}}
                if 'data' in raw_data and isinstance(raw_data['data'], dict):
                    invoice_data = raw_data['data']
                    structure = invoice_data.get('structure', {})
                    invoices = invoice_data.get('invoices', [])
                    master_header = structure.get('master', [])
                    detail_header = structure.get('detail', [])
                    if invoices:
                        master_values = invoices[0].get('master', [])
                        raw_master = dict(zip(master_header, master_values))
                        sql_mapped_master = self._map_json_to_sql_fields(raw_master, is_master=True)
                        parsed['master'] = sql_mapped_master

                        detail_rows = invoices[0].get('detail', [])
                        parsed['detail'] = []
                        for row in detail_rows:
                            raw_detail_row = dict(zip(detail_header, row))
                            sql_mapped_detail = self._map_json_to_sql_fields(raw_detail_row, is_master=False)
                            parsed['detail'].append(sql_mapped_detail)

                    for key, value in raw_data.items():
                        if key != 'data': parsed['other'][key] = value
                return parsed

        parser = JsonParser()
        parsed_data = parser._parse_json_input_data(self.sample_json)

        master_data = parsed_data["master"]
        detail_data = parsed_data["detail"]

        # Kiểm tra các field số được chuyển đổi đúng
        self.assertIsInstance(master_data.get("Tổng_tiền_hàng"), (int, float))
        self.assertIsInstance(master_data.get("Tổng_tiền_thanh_toán"), (int, float))

        # Kiểm tra detail data
        if detail_data:
            first_detail = detail_data[0]
            self.assertIsInstance(first_detail.get("Số_lượng"), (int, float))
            self.assertIsInstance(first_detail.get("Giá"), (int, float))
            self.assertIsInstance(first_detail.get("Tiền_hàng"), (int, float))

def run_tests():
    """Chạy tất cả tests"""
    print("=== BẮT ĐẦU TEST CHỨC NĂNG PHÂN TÍCH HÓA ĐƠN ===\n")
    
    # Tạo test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestInvoiceAnalysis)
    
    # Chạy tests với verbose output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print(f"\n=== KẾT QUẢ TEST ===")
    print(f"Tổng số tests: {result.testsRun}")
    print(f"Thành công: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"Thất bại: {len(result.failures)}")
    print(f"Lỗi: {len(result.errors)}")
    
    if result.failures:
        print("\n=== CHI TIẾT THẤT BẠI ===")
        for test, traceback in result.failures:
            print(f"Test: {test}")
            print(f"Traceback: {traceback}")
    
    if result.errors:
        print("\n=== CHI TIẾT LỖI ===")
        for test, traceback in result.errors:
            print(f"Test: {test}")
            print(f"Traceback: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
