[{"doc_type": "<PERSON><PERSON><PERSON> b<PERSON> hàng", "name": "[Thông tin chung] Tổng tiền thanh toán không được âm.", "condition": "SELECT 1 FROM master WHERE Tổng_tiền_thanh_toán < 0"}, {"doc_type": "<PERSON><PERSON><PERSON> b<PERSON> hàng", "name": "[Thông tin chung] Tên đơn vị mua hàng không được để trống.", "condition": "SELECT 1 FROM master WHERE Tên_đơn_vị_mua_hàng IS NULL OR Tên_đơn_vị_mua_hàng = ''"}, {"doc_type": "<PERSON><PERSON><PERSON> b<PERSON> hàng", "name": "[<PERSON> tiết] <PERSON><PERSON> dòng hàng với số lượng nhỏ hơn hoặc bằng 0.", "condition": "SELECT 1 FROM details WHERE Số_lượng <= 0"}, {"doc_type": "<PERSON><PERSON><PERSON> b<PERSON> hàng", "name": "[<PERSON> tiết] <PERSON><PERSON> dòng hàng với đơn giá âm.", "condition": "SELECT 1 FROM details WHERE Giá < 0"}, {"doc_type": "<PERSON><PERSON><PERSON> b<PERSON> hàng", "name": "[<PERSON><PERSON><PERSON> chi<PERSON>] Tổng tiền hàng chi tiết không khớp với tổng tiền hàng chung.", "condition": "SELECT 1 FROM master WHERE ABS(Tổng_tiền_hàng - (SELECT SUM(Tiền_hàng) FROM details)) > 1"}, {"doc_type": "<PERSON><PERSON><PERSON> b<PERSON> hàng", "name": "[<PERSON><PERSON><PERSON> chiếu] Tổng tiền thuế chi tiết không khớp với tổng tiền thuế chung.", "condition": "SELECT 1 FROM master WHERE ABS(Tổng_tiền_thuế - (SELECT SUM(Tiền_thuế) FROM details)) > 1"}, {"doc_type": "<PERSON><PERSON><PERSON> b<PERSON> hàng", "name": "[Chi tiết] Thành tiền của một dòng không bằng (Số lượng * Giá).", "condition": "SELECT 1 FROM details WHERE ABS(Tiền_hàng - (<PERSON><PERSON>_lượng * Giá)) > 1"}, {"doc_type": "<PERSON><PERSON><PERSON> xu<PERSON>t kho", "name": "[Thông tin chung] Kho xuất và kho nhập không được trùng nhau.", "condition": "SELECT 1 FROM master WHERE Xuất_tại_kho = Nhập_tại_kho"}, {"doc_type": "<PERSON><PERSON><PERSON> xu<PERSON>t kho", "name": "[Thông tin chung] Tên người vận chuyển không được để trống.", "condition": "SELECT 1 FROM master WHERE Người_vận_chuyển IS NULL OR Người_vận_chuyển = ''"}, {"doc_type": "<PERSON><PERSON><PERSON> xu<PERSON>t kho", "name": "[<PERSON> tiết] <PERSON><PERSON> dòng hàng với số lượng bằng 0 hoặc âm.", "condition": "SELECT 1 FROM details WHERE Số_lượng <= 0"}, {"doc_type": "<PERSON><PERSON><PERSON> xu<PERSON>t kho", "name": "[<PERSON><PERSON><PERSON> chi<PERSON>] Tổng tiền hàng chi tiết không khớp với tổng tiền hàng chung.", "condition": "SELECT 1 FROM master WHERE ABS(Tổng_tiền_hàng - (SELECT SUM(Tiền_hàng) FROM details)) > 1"}]