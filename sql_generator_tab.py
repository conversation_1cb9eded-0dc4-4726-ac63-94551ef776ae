import tkinter as tk
from tkinter import messagebox, Toplevel
import ttkbootstrap as ttk
from ttkbootstrap.scrolled import ScrolledText
import csv
import io
import json
import os
from datetime import datetime
import re

# ===================================================================================
# MODEL - Logic xử lý dữ liệu
# ===================================================================================
class SqlGeneratorModel:
    """
    Chứa logic để tạo script và quản lý log backup.
    """
    LOG_FILE = 'backup_log.json'

    def __init__(self):
        if not os.path.exists(self.LOG_FILE):
            with open(self.LOG_FILE, 'w', encoding='utf-8') as f:
                json.dump([], f)

    def get_backup_logs(self) -> list:
        try:
            with open(self.LOG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []

    def log_backup(self, log_entry: dict):
        logs = self.get_backup_logs()
        logs.insert(0, log_entry)
        with open(self.LOG_FILE, 'w', encoding='utf-8') as f:
            json.dump(logs, f, indent=4, ensure_ascii=False)

    def delete_logs(self, logs_to_delete: list[dict]):
        """Xóa các log được chỉ định khỏi file JSON."""
        current_logs = self.get_backup_logs()
        keys_to_delete = {log['backup_table'] for log in logs_to_delete}
        updated_logs = [log for log in current_logs if log['backup_table'] not in keys_to_delete]
        with open(self.LOG_FILE, 'w', encoding='utf-8') as f:
            json.dump(updated_logs, f, indent=4, ensure_ascii=False)

    def _is_numeric(self, value: str) -> bool:
        if value is None or value.strip() == '': return False
        try:
            float(value.replace(',', ''))
            return True
        except (ValueError, TypeError):
            return False

    def _is_datetime(self, value: str) -> bool:
        if value is None or value.strip() == '': return False
        datetime_pattern = r'^\d{4}-\d{2}-\d{2}(\s\d{2}:\d{2}:\d{2}(\.\d{1,3})?)?$'
        return re.match(datetime_pattern, value.strip()) is not None

    def _has_header_heuristic(self, lines: list) -> bool:
        if not lines: return False
        first_line = lines[0]
        if not any(cell.strip() for cell in first_line): return False
        for cell in first_line:
            clean_cell = cell.strip()
            if not clean_cell: continue
            if self._is_numeric(clean_cell) or self._is_datetime(clean_cell): return False
        return True

    def generate_bulk_insert_script(self, table_name: str, grid_data: str, delete_first: bool, delete_condition: str, backup_first: bool) -> tuple[str, str, dict | None]:
        clean_table_name = table_name.strip()
        if not clean_table_name: return "-- LỖI: Vui lòng nhập tên bảng.", "Lỗi", None
        if not grid_data.strip(): return "-- LỖI: Vui lòng dán dữ liệu vào ô.", "Lỗi", None

        try:
            f = io.StringIO(grid_data)
            reader = csv.reader(f, delimiter='\t', quotechar='"')
            lines = [row for row in reader if row]
        except csv.Error as e:
            return f"-- LỖI CSV: {e}", "Lỗi phân tích dữ liệu", None

        if not lines: return "-- LỖI: Không tìm thấy dữ liệu.", "Lỗi dữ liệu", None
        
        if not self._has_header_heuristic(lines):
            error_msg = "-- LỖI: Dữ liệu thiếu header hoặc không đúng định dạng.\n\nDòng đầu tiên phải là tiêu đề cột và không được chứa giá trị số hoặc ngày tháng."
            return error_msg, "Lỗi Header", None

        header = [col.strip() for col in lines[0]]
        data_rows = lines[1:]
        column_list_str = ", ".join([f'[{col}]' for col in header])
        script_parts = []
        backup_info = None

        script_parts.append(f"--//// dePro /////// At: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')} /////////////////////////\n")

        if delete_first:
            clean_condition = delete_condition.strip()
            if clean_condition and backup_first:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_table_name = f"{clean_table_name}_Backup_{timestamp}"
                script_parts.append(f"SELECT * INTO [{backup_table_name}] FROM [{clean_table_name}] WHERE {clean_condition}\n")
                backup_info = {"timestamp": datetime.now().strftime('%d/%m/%Y %H:%M:%S'), "main_table": clean_table_name,
                               "backup_table": backup_table_name, "condition": clean_condition}
            delete_statement = f"DELETE [{clean_table_name}]"
            if clean_condition:
                delete_statement += f" WHERE {clean_condition}"
            script_parts.append(delete_statement)

        script_parts.append(f"SELECT {column_list_str} INTO #data FROM [{clean_table_name}] WHERE 1 = 0")

        successful_rows, skipped_rows = 0, 0
        for row in data_rows:
            values = []
            for i in range(len(header)):
                clean_value = row[i].strip() if i < len(row) else ""
                if clean_value.lower() == 'null':
                    values.append("NULL")
                elif self._is_numeric(clean_value):
                    values.append(clean_value.replace(',', ''))
                elif self._is_datetime(clean_value):
                    values.append(f"'{clean_value}'")
                else:
                    values.append(f"N'{clean_value.replace("'", "''")}'")
            if values:
                script_parts.append(f"INSERT INTO #data VALUES({', '.join(values)})")
                successful_rows += 1
            else:
                 skipped_rows +=1

        script_parts.append(f"INSERT INTO [{clean_table_name}] SELECT * FROM #data\n")
        script_parts.append("DROP TABLE #data\n")
        script_parts.append("GO")

        status_message = f"Tạo script thành công cho {successful_rows} dòng."
        if skipped_rows > 0: status_message += f" ({skipped_rows} dòng trống bị bỏ qua)."
        
        return "\n".join(script_parts), status_message, backup_info

# ===================================================================================
# VIEW & CONTROLLER - Cửa sổ quản lý Log
# ===================================================================================
class BackupLogView(Toplevel):
    def __init__(self, parent, controller):
        super().__init__(parent)
        self.title("Quản lý Log Backup")
        self.geometry("900x600")
        self.controller = controller
        self.transient(parent)
        self.grab_set()

        # --- Top Frame for buttons ---
        top_frame = ttk.Frame(self, padding=10)
        top_frame.pack(side="top", fill="x", pady=(0, 5))
        
        self.refresh_button = ttk.Button(top_frame, text="Tải lại", command=self.controller.refresh_logs, bootstyle="info")
        self.refresh_button.pack(side="left")

        # --- Bottom Frame for script display ---
        script_frame = ttk.LabelFrame(self, text="Script Phục hồi", padding=10)
        script_frame.pack(side="bottom", fill="x", padx=10, pady=(0, 10))
        script_frame.grid_columnconfigure(0, weight=1)
        
        self.script_display = ScrolledText(script_frame, height=5, wrap=tk.WORD, autohide=True)
        self.script_display.grid(row=0, column=0, sticky="ew")
        
        self.copy_script_button = ttk.Button(script_frame, text="Sao chép Script", command=self.controller.copy_selected_script, state="disabled")
        self.copy_script_button.grid(row=0, column=1, sticky="ne", padx=(10, 0))

        # --- Middle Frame for Treeview (this will expand) ---
        tree_frame = ttk.Frame(self)
        tree_frame.pack(side="top", expand=True, fill="both", padx=10)
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        self.log_view = ttk.Treeview(tree_frame, show="headings", columns=("time", "backup"))
        self.log_view.grid(row=0, column=0, sticky="nsew")

        v_scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=self.log_view.yview)
        v_scroll.grid(row=0, column=1, sticky="ns")
        self.log_view.configure(yscrollcommand=v_scroll.set)

        h_scroll = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.log_view.xview)
        h_scroll.grid(row=1, column=0, sticky="ew")
        self.log_view.configure(xscrollcommand=h_scroll.set)

        # CẬP NHẬT: Tăng độ rộng cột thời gian
        self.log_view.heading("time", text="Thời gian"); self.log_view.column("time", width=200, stretch=False)
        self.log_view.heading("backup", text="Bảng Backup"); self.log_view.column("backup", width=400)
        
        self.log_view.bind("<<TreeviewSelect>>", self.controller.on_log_selection_change)
        self.log_view.bind("<Delete>", self.controller.delete_selected_logs)
        self.log_view.bind("<Control-a>", self.select_all_logs)

        # CẬP NHẬT: Canh giữa cửa sổ
        self.center_window()

    def center_window(self):
        """Canh giữa cửa sổ Toplevel so với cửa sổ cha."""
        self.update_idletasks()
        parent = self.master
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.winfo_height() // 2)
        self.geometry(f"+{x}+{y}")

    def select_all_logs(self, event=None):
        self.log_view.selection_set(self.log_view.get_children())
        return "break"

    def update_logs(self, logs: list):
        for item in self.log_view.get_children():
            self.log_view.delete(item)
            
        for log in logs:
            self.log_view.insert("", "end", iid=log['backup_table'], 
                                 values=(log["timestamp"], log["backup_table"]))

    def update_script_display(self, script_text: str):
        self.script_display.text.config(state="normal")
        self.script_display.delete("1.0", tk.END)
        self.script_display.insert("1.0", script_text)
        self.script_display.text.config(state="disabled")

class BackupLogController:
    def __init__(self, parent_view, model):
        self.model = model
        self.view = BackupLogView(parent_view, self)
        self.refresh_logs()

    def refresh_logs(self):
        self.view.update_logs(self.model.get_backup_logs())
        self.on_log_selection_change()

    def on_log_selection_change(self, event=None):
        selected_items = self.view.log_view.selection()
        
        if len(selected_items) == 1:
            self.view.copy_script_button.config(state="normal")
            
            selected_iid = selected_items[0]
            all_logs = self.model.get_backup_logs()
            selected_log = next((log for log in all_logs if log['backup_table'] == selected_iid), None)

            if selected_log:
                main_table = selected_log['main_table']
                backup_table = selected_log['backup_table']
                script = (f"INSERT INTO [{main_table}]\nSELECT * FROM [{backup_table}];\n\n"
                          f"-- Gợi ý: Sau khi phục hồi, chạy lệnh sau để xóa bảng backup:\n"
                          f"-- DROP TABLE [{backup_table}];")
                self.view.update_script_display(script)
            else:
                self.view.update_script_display("")
        else:
            self.view.copy_script_button.config(state="disabled")
            self.view.update_script_display("")

    def delete_selected_logs(self, event=None):
        selected_iids = self.view.log_view.selection()
        if not selected_iids: return
        
        count = len(selected_iids)
        if not messagebox.askyesno("Xác nhận xóa", f"Bạn có chắc chắn muốn xóa {count} log đã chọn không?"):
            return
        
        all_logs = self.model.get_backup_logs()
        logs_to_delete = [log for log in all_logs if log['backup_table'] in selected_iids]
        
        self.model.delete_logs(logs_to_delete)
        self.refresh_logs()

    def copy_selected_script(self):
        script_text = self.view.script_display.get("1.0", tk.END).strip()
        if script_text:
            self.view.clipboard_clear()
            self.view.clipboard_append(script_text)
            messagebox.showinfo("Thành công", "Đã sao chép Script Phục hồi vào clipboard.", parent=self.view)

# ===================================================================================
# VIEW - Giao diện chính của Tab
# ===================================================================================
class SqlGeneratorTab(ttk.Frame):
    def __init__(self, parent):
        super().__init__(parent, padding=15)
        self.model = SqlGeneratorModel()
        self.log_window = None
        self.grid_rowconfigure(4, weight=1); self.grid_rowconfigure(7, weight=2) 
        self.grid_columnconfigure(1, weight=1)
        self._create_widgets()
        
    def _create_widgets(self):
        top_frame = ttk.Frame(self)
        top_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        ttk.Label(top_frame, text="Tên Bảng:").pack(side="left", padx=(0, 5))
        self.table_name_entry = ttk.Entry(top_frame, width=40)
        self.table_name_entry.pack(side="left", expand=True, fill="x")
        ttk.Button(top_frame, text="Quản lý Log Backup", command=self.show_backup_log_window, bootstyle="secondary-outline").pack(side="right", padx=(10, 0))

        ttk.Label(self, text="Điều kiện Xóa (WHERE):").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.delete_condition_entry = ttk.Entry(self, width=50)
        self.delete_condition_entry.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

        options_frame = ttk.LabelFrame(self, text="Tùy chọn", padding=10)
        options_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        self.delete_var = tk.BooleanVar()
        self.delete_check = ttk.Checkbutton(options_frame, text="Xóa dữ liệu cũ theo điều kiện", variable=self.delete_var, command=self.toggle_delete_options)
        self.delete_check.pack(side="left", anchor="w")
        self.backup_var = tk.BooleanVar()
        self.backup_check = ttk.Checkbutton(options_frame, text="Tạo bảng sao lưu (backup) cho dữ liệu bị xóa", variable=self.backup_var, state="disabled")
        self.backup_check.pack(side="left", anchor="w", padx=20)
        
        ttk.Label(self, text="Dán dữ liệu từ Excel/Grid (dòng đầu phải là header):").grid(row=3, column=0, columnspan=2, padx=5, pady=(10, 5), sticky="w")
        self.data_input_text = ScrolledText(self, wrap=tk.NONE, height=10)
        self.data_input_text.grid(row=4, column=0, columnspan=2, padx=5, pady=5, sticky="nsew")

        ttk.Button(self, text="Tạo Script Bulk Insert SQL", command=self.handle_generate_sql, bootstyle="primary").grid(row=5, column=0, columnspan=2, padx=5, pady=10)

        result_frame = ttk.Frame(self)
        result_frame.grid(row=6, column=0, columnspan=2, padx=5, pady=(10, 5), sticky="ew")
        ttk.Label(result_frame, text="Kết quả: Script SQL").pack(side="left")
        ttk.Button(result_frame, text="Sao chép", command=self.copy_to_clipboard, bootstyle="info-outline").pack(side="right")

        self.result_output_text = ScrolledText(self, wrap=tk.WORD, height=15, autohide=True)
        self.result_output_text.text.config(state='disabled')
        self.result_output_text.grid(row=7, column=0, columnspan=2, padx=5, pady=5, sticky="nsew")
        self.status_bar = ttk.Label(self, text="Sẵn sàng", anchor="w", bootstyle="secondary")
        self.status_bar.grid(row=8, column=0, columnspan=2, sticky="ew", padx=5, pady=(5,0))
        
        self.delete_condition_entry.bind("<KeyRelease>", self.on_condition_entry_type)
        
    def handle_generate_sql(self):
        input_data = {"table_name": self.table_name_entry.get(), "grid_data": self.data_input_text.get("1.0", tk.END),
                      "delete_first": self.delete_var.get(), "delete_condition": self.delete_condition_entry.get(),
                      "backup_first": self.backup_var.get()}
        if not input_data["table_name"].strip() or not input_data["grid_data"].strip():
            messagebox.showerror("Lỗi", "Tên bảng và Dữ liệu không được để trống.")
            return

        sql_script, status, backup_info = self.model.generate_bulk_insert_script(**input_data)
        if backup_info:
            self.model.log_backup(backup_info)
            status += " Đã ghi log backup."
        self.set_sql_result(sql_script)
        self.update_status(status)
        if "Lỗi" in status: 
            messagebox.showerror("Lỗi tạo Script", sql_script)

    def show_backup_log_window(self):
        if self.log_window is None or not self.log_window.winfo_exists():
            self.log_window = BackupLogController(self, self.model).view
        else:
            self.log_window.lift()

    def on_condition_entry_type(self, event=None):
        has_text = bool(self.delete_condition_entry.get().strip())
        self.delete_var.set(has_text)
        self.toggle_delete_options()
        if has_text:
            self.backup_var.set(True)

    def toggle_delete_options(self):
        state = "normal" if self.delete_var.get() else "disabled"
        self.backup_check.config(state=state)
        if not self.delete_var.get():
            self.backup_var.set(False)

    def set_sql_result(self, script: str):
        self.result_output_text.text.config(state='normal')
        self.result_output_text.delete("1.0", tk.END)
        self.result_output_text.insert(tk.END, script)
        self.result_output_text.text.config(state='disabled')

    def update_status(self, message: str):
        self.status_bar.config(text=message)

    def copy_to_clipboard(self):
        script_content = self.result_output_text.get("1.0", tk.END)
        if script_content.strip() and "LỖI" not in script_content:
            self.clipboard_clear(); self.clipboard_append(script_content)
            self.update_status("Đã sao chép vào clipboard!")
        else:
            self.update_status("Không có gì để sao chép hoặc script bị lỗi.")
